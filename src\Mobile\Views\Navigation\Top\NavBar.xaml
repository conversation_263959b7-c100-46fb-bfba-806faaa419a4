﻿<?xml version="1.0" encoding="UTF-8"?>

<VerticalStackLayout
    x:Class="AppoMobi.UI.NavBar"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HorizontalOptions="Fill"
    Spacing="0"
    VerticalOptions="Start">

    <!--  status bar  -->
    <!--<BoxView
    BackgroundColor="Pink"
    HeightRequest="{x:Static draw:Super.StatusBarHeight}"
    HorizontalOptions="Fill">
    -->
    <!--<BoxView.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
            <GradientStop Offset="0.0" Color="{x:Static xam:BackColors.GradientStartNav}" />
            <GradientStop Offset="1.0" Color="{x:Static xam:BackColors.GradientEndNav}" />
        </LinearGradientBrush>
    </BoxView.Background>-->
    <!--
    </BoxView>-->

    <Grid HeightRequest="46" HorizontalOptions="Fill">

        <draw:Canvas
            x:Name="MainCanvas"
            Gestures="Enabled"
            HorizontalOptions="Fill"
            UpdateMode="Dynamic"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">
                <draw:SkiaLayout.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Offset="0.0" Color="{x:Static xam:BackColors.GradientStartNav}" />
                        <GradientStop Offset="1.0" Color="{x:Static xam:BackColors.GradientEndNav}" />
                    </LinearGradientBrush>
                </draw:SkiaLayout.Background>

                <!--  TITLE  -->
                <draw:SkiaRichLabel
                    x:Name="txtTitle"
                    FontFamily="FontTextTitle"
                    FontSize="16"
                    HorizontalOptions="Center"
                    Opacity="{x:Static xam:PageOptions.NavTitleOpacity}"
                    Text="ArtOfFoto"
                    TextColor="{x:Static appoMobi:AppColors.Icons}"
                    UseCache="Operations"
                    VerticalOptions="Fill"
                    VerticalTextAlignment="Center" />

                <!--  icon LEFT 1  -->
                <draw:SkiaImage
                    x:Name="LeftIcon1"
                    Margin="16,0,8,0"
                    AddEffect="Tint"
                    Aspect="AspectFit"
                    ColorTint="{x:Static appoMobi:AppColors.Icons}"
                    HeightRequest="{x:Static appoMobi:AppUI.NavbarIconSize}"
                    HorizontalOptions="Start"
                    Opacity="{x:Static xam:PageOptions.LeftIconOpacity}"
                    RescalingQuality="High"
                    UseCache="Image"
                    VerticalOptions="Center" />

            </draw:SkiaLayout>
        </draw:Canvas>

        <Grid
            x:Name="cNavBarSlider"
            ColumnSpacing="0"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <!--  main layout - title and icons  -->
            <Grid
                x:Name="cTitleBarMain"
                Grid.Column="0"
                Padding="0"
                HorizontalOptions="Fill"
                IsVisible="True">

                <!--<forms:CachedImage
                    Opacity="0.3"
                    Aspect="AspectFill"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill"
                   Source="{x:Static appoMobi:AppUI.NavigationBackgroundImage}"/>-->

                <!--  title label  fil  -->

                <!--  title image, unused  -->
                <forms:CachedImage
                    x:Name="TitleLogo"
                    Margin="0,11,0,11"
                    Aspect="AspectFit"
                    FadeAnimationEnabled="False"
                    IsVisible="False"
                    LoadingPriority="Highest"
                    VerticalOptions="Fill" />

                <!--  xam:LabelAlwaysFit  -->
                <!--<xam:LabelAlwaysFit
                x:Name="txtTitle"
                FontFamily="ui"
                FontSize="16"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                MinimumWidthRequest="40"
                Opacity="{x:Static xam:PageOptions.NavTitleOpacity}"
                Text="ArtOfFoto"
                TextColor="{x:Static appoMobi:AppColors.Icons}"
                VerticalOptions="Fill"
                VerticalTextAlignment="Center" />-->


                <!--  icon RIGHT 1  -->
                <forms:CachedImage
                    x:Name="RightIcon1"
                    Margin="16,0,16,0"
                    FadeAnimationEnabled="False"
                    HeightRequest="{x:Static appoMobi:AppUI.NavbarIconSize}"
                    HorizontalOptions="End"
                    LoadingPriority="Highest"
                    Opacity="{x:Static xam:PageOptions.RightIconsOpacity}"
                    VerticalOptions="Center"
                    WidthRequest="{x:Static appoMobi:AppUI.NavbarIconSize}">
                    <forms:CachedImage.Transformations>
                        <transformations:TintTransformation EnableSolidColor="True"
                                                            HexColor="{x:Static appoMobi:AppColors.icons}" />
                    </forms:CachedImage.Transformations>
                </forms:CachedImage>

                <!--  icon RIGHT 1 hotspot  -->
                <gestures:LegacyGesturesGrid
                    x:Name="hsRightIcon1"
                    Margin="0,0,9,0"
                    Down="OnDown_RightIcon1"
                    HorizontalOptions="End"
                    VerticalOptions="Fill"
                    WidthRequest="36">

                    <!--  icon RIGHT 1 SYMBOL  -->
                    <xam:FontIconLabel
                        x:Name="RightIcon1txt"
                        FontSize="{x:Static xam:FontSizes.NavbarIcon}"
                        HorizontalOptions="Center"
                        Opacity="{x:Static xam:PageOptions.RightIconsOpacity}"
                        TextColor="{x:Static appoMobi:AppColors.IconsNavRight}"
                        VerticalOptions="Center" />

                </gestures:LegacyGesturesGrid>


                <!--  icon RIGHT 2  -->
                <forms:CachedImage
                    x:Name="RightIcon2"
                    Margin="8,0,54,0"
                    FadeAnimationEnabled="False"
                    HeightRequest="{x:Static appoMobi:AppUI.NavbarIconSize}"
                    HorizontalOptions="End"
                    LoadingPriority="Highest"
                    Opacity="{x:Static xam:PageOptions.RightIconsOpacity}"
                    VerticalOptions="Center"
                    WidthRequest="{x:Static appoMobi:AppUI.NavbarIconSize}">
                    <forms:CachedImage.Transformations>
                        <transformations:TintTransformation EnableSolidColor="True"
                                                            HexColor="{x:Static appoMobi:AppColors.icons}" />
                    </forms:CachedImage.Transformations>
                </forms:CachedImage>

                <!--  icon RIGHT 2 hotspot  -->
                <gestures:LegacyGesturesGrid
                    x:Name="hsRightIcon2"
                    Margin="0,0,45,0"
                    Down="OnDown_RightIcon2"
                    HorizontalOptions="End"
                    VerticalOptions="Fill"
                    WidthRequest="38">

                    <!--  icon RIGHT 2 SYMBOL  -->
                    <xam:FontIconLabel
                        x:Name="RightIcon2txt"
                        FontSize="{x:Static xam:FontSizes.NavbarIcon}"
                        HorizontalOptions="Center"
                        Opacity="{x:Static xam:PageOptions.RightIconsOpacity}"
                        TextColor="{x:Static appoMobi:AppColors.IconsNavRight}"
                        VerticalOptions="Center" />
                </gestures:LegacyGesturesGrid>

                <!--  icon LEFT 1  -->
                <!--<forms:CachedImage
                x:Name="LeftIcon1"
                Margin="16,0,8,0"
                Aspect="AspectFit"
                FadeAnimationEnabled="False"
                HeightRequest="{x:Static appoMobi:AppUI.NavbarIconSize}"
                HorizontalOptions="Start"
                LoadingPriority="Highest"
                Opacity="{x:Static xam:PageOptions.LeftIconOpacity}"
                Source="Images/close3a.png"
                VerticalOptions="Center"
                WidthRequest="{x:Static appoMobi:AppUI.NavbarIconSize}">
                <forms:CachedImage.Transformations>
                    <transformations:TintTransformation EnableSolidColor="True" HexColor="{x:Static appoMobi:AppColors.icons}" />
                </forms:CachedImage.Transformations>
            </forms:CachedImage>-->

                <!--  icon LEFT 1 hotspot  -->
                <gestures:LegacyGesturesGrid
                    x:Name="hsLeftIcon1"
                    Margin="8,0,0,0"
                    Down="OnDown_LeftIcon1"
                    HorizontalOptions="Start"
                    VerticalOptions="Fill"
                    WidthRequest="64">
                    <xam:FontIconLabel
                        x:Name="LeftIcon1txt"
                        Margin="7,0,0,0"
                        FontSize="{x:Static xam:FontSizes.NavbarIcon}"
                        HorizontalOptions="Start"
                        Opacity="{x:Static xam:PageOptions.LeftIconOpacity}"
                        TextColor="{x:Static appoMobi:AppColors.Icons}"
                        VerticalOptions="Center" />

                </gestures:LegacyGesturesGrid>


                <!--  icon LEFT 2  -->
                <forms:CachedImage
                    x:Name="LeftIcon2"
                    Margin="51,0,8,0"
                    FadeAnimationEnabled="False"
                    HeightRequest="{x:Static appoMobi:AppUI.NavbarIconSize}"
                    HorizontalOptions="Start"
                    LoadingPriority="Highest"
                    Opacity="{x:Static xam:PageOptions.RightIconsOpacity}"
                    VerticalOptions="Center"
                    WidthRequest="{x:Static appoMobi:AppUI.NavbarIconSize}">
                    <forms:CachedImage.Transformations>
                        <transformations:TintTransformation EnableSolidColor="True"
                                                            HexColor="{x:Static appoMobi:AppColors.icons}" />
                    </forms:CachedImage.Transformations>
                </forms:CachedImage>

                <!--  icon LEFT 2 hotspot  -->
                <gestures:LegacyGesturesGrid
                    x:Name="hsLeftIcon2"
                    Margin="45,0,0,0"
                    Down="OnDown_LeftIcon2"
                    HorizontalOptions="Start"
                    VerticalOptions="Fill"
                    WidthRequest="36">

                    <!--  icon LEFT 2 SYMBOL  -->
                    <xam:FontIconLabel
                        x:Name="LeftIcon2txt"
                        FontSize="{x:Static xam:FontSizes.NavbarIcon}"
                        HorizontalOptions="Center"
                        Opacity="{x:Static xam:PageOptions.RightIconsOpacity}"
                        TextColor="{x:Static appoMobi:AppColors.Icons}"
                        VerticalOptions="Center" />
                </gestures:LegacyGesturesGrid>


            </Grid>

            <!--  TitleBar DropShadow  -->
            <!--<BoxView
                BackgroundColor="{x:Static appoMobi:AppColors.DividerNavTabs}"
                HeightRequest="1"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="End" />-->

        </Grid>

    </Grid>

</VerticalStackLayout>