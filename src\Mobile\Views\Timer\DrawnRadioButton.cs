﻿using DrawnUi.Controls;

namespace AppoMobi.Main
{
    public partial class DrawnRadioButton : SkiaRadioButton
    {
        private readonly Color _color = Colors.White;

        public DrawnRadioButton(Color accent)
        {
            _color = accent;
            Build();
        }

        public DrawnRadioButton()
        {
            Build();
        }

        protected void Build()
        {
            UseCache = SkiaCacheType.Image;
            MinimumHeightRequest = 24;
            Children = new List<SkiaControl>()
            {
                new SkiaLayout()
                {
                    HeightRequest = 16,
                    LockRatio = 1,
                    VerticalOptions = LayoutOptions.Center,
                    Children =
                    {
                        new SkiaShape()
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Fill,
                            StrokeColor = _color,
                            StrokeWidth = 1.5,
                            Type = ShapeType.Circle
                        },
                        new SkiaShape()
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Fill,
                            BackgroundColor = _color,
                            Margin = 4,
                            Type = ShapeType.Circle,
                            Tag = "On"
                        },
                    }
                },
                new SkiaRichLabel()
                {
                    Margin = new(24, 0, 0, 0),
                    FontSize = 14,
                    MaxLines = 2,
                    Tag = "Text",
                    FontFamily = "FontTextTitle",
                    TextColor = _color,
                    VerticalOptions = LayoutOptions.Center
                }
            };
        }

        /*
        public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args,
            GestureEventProcessingInfo apply)
        {
            if (args.Type == TouchActionResult.Tapped)
            {
                var ptsInsideControl = GetOffsetInsideControlInPoints(args.Event.Location, apply.ChildOffset);
                this.PlayRippleAnimation(Colors.CornflowerBlue, ptsInsideControl.X, ptsInsideControl.Y);
            }

            return base.ProcessGestures(args, apply);
        }
        */
    }
}
