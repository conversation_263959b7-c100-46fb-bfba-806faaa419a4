using AppoMobi.Mobile.Views.Camera.Exposure;

namespace AppoMobi.Main
{
    /// <summary>
    /// Example usage of the stop interval functionality in ExposureMeter
    /// This file demonstrates how to switch between different stop intervals
    /// </summary>
    public static class ExposureMeterStopIntervalExample
    {
        /// <summary>
        /// Example: Switch exposure meter to full stops (traditional photography)
        /// </summary>
        public static void SwitchToFullStops(ExposureMeter exposureMeter)
        {
            exposureMeter.SetStopInterval(ExposureData.StopInterval.FullStop);
            // Now all wheels will show only full stop values:
            // Aperture: f/1.0, f/1.4, f/2.0, f/2.8, f/4.0, f/5.6, f/8.0, f/11, f/16, f/22, f/32
            // Shutter: 1/8000, 1/4000, 1/2000, 1/1000, 1/500, 1/250, 1/125, 1/60, 1/30, 1/15, 1/8, 1/4, 0.5, 1, 2, 4, 8, 15, 30
            // ISO: 50, 100, 200, 400, 800, 1600, 3200, 6400, 12800, 25600, 51200, 102400
        }

        /// <summary>
        /// Example: Switch exposure meter to 1/2 stops (medium precision)
        /// </summary>
        public static void SwitchToHalfStops(ExposureMeter exposureMeter)
        {
            exposureMeter.SetStopInterval(ExposureData.StopInterval.HalfStop);
            // Now all wheels will show 1/2 stop intervals with intermediate values
        }

        /// <summary>
        /// Example: Switch exposure meter to 1/3 stops (most common, finest control)
        /// </summary>
        public static void SwitchToThirdStops(ExposureMeter exposureMeter)
        {
            exposureMeter.SetStopInterval(ExposureData.StopInterval.ThirdStop);
            // This is the default setting - most comprehensive range
        }

        /// <summary>
        /// Example: Get current stop interval setting
        /// </summary>
        public static ExposureData.StopInterval GetCurrentInterval(ExposureMeter exposureMeter)
        {
            return exposureMeter.CurrentStopInterval;
        }

        /// <summary>
        /// Example: Create a settings UI method that could be called from your settings screen
        /// </summary>
        public static void ApplyUserStopIntervalPreference(ExposureMeter exposureMeter, int userChoice)
        {
            var interval = userChoice switch
            {
                0 => ExposureData.StopInterval.FullStop,    // "Full stops"
                1 => ExposureData.StopInterval.HalfStop,    // "1/2 stops" 
                2 => ExposureData.StopInterval.ThirdStop,   // "1/3 stops"
                _ => ExposureData.StopInterval.ThirdStop    // Default
            };

            exposureMeter.SetStopInterval(interval);
        }

        /// <summary>
        /// Example: Get available stop interval options for UI
        /// </summary>
        public static string[] GetStopIntervalDisplayNames()
        {
            return new[]
            {
                "Full stops (1 stop)",
                "Half stops (1/2 stop)", 
                "Third stops (1/3 stop)"
            };
        }
    }
}
