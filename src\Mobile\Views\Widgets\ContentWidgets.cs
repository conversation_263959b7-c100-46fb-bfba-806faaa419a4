﻿namespace AppoMobi.Main
{
    /// <summary>
    /// Temporary wrapper include drawn inside non-drawn
    /// </summary>
    public class ContentWidgets : ScreenCanvas
    {
        public ContentWidgets()
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            Content = new ScreenWidgets() { };
        }

        public ContentWidgets(IPageEnhancedNav daddy)
        {
            Daddy = daddy;

            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            Content = new ScreenWidgets() { };
        }
    }
}
