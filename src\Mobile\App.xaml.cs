﻿using System.Diagnostics;
using System.Globalization;
using System.Net;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using AppoMobi.Common.Dto;
using AppoMobi.Forms.Content.Camera.Models;
using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Mobile.Views.Popups;
using AppoMobi.Tenant;
using DrawnUi.Infrastructure.Enums;
using DrawnUi.Views;
using Newtonsoft.Json;


namespace AppoMobi.Mobile
{
    public partial class App : Application
    {


#if xDEBUG
        private bool mainPageOverride = true;

        protected override Window CreateWindow(IActivationState? activationState)
        {
            return new Window(new DevPage());
        }

#else
        private bool mainPageOverride = false;
#endif

        private bool _Loaded;
        public bool Loaded
        {
            get { return _Loaded; }
            set
            {
                if (_Loaded != value)
                {
                    _Loaded = value;
                    OnPropertyChanged();
                }
            }
        }

        public App(IServiceProvider services)
        {
            if (Loaded)
            {
                InitializeComponent();
            }
            else
            {
                Loaded = true;

                Services = services;

                //========================================================
                //init required data - LEGACY
                //========================================================

                #region BUILD_CHANGED

                //maybe reset settings if build changed
                var build = Core.Native.GetAppBuild(); //cyService.Get<INiftyHelpers>().GetAppBuild();
                if (Settings.Current.SelectedBuild != build)
                {
                    //build changed

                    //todo stuff you want when build changed
                    // reset some settings etc

                    Settings.Current.ShowInputHours = false;
                    //Settings.Current.FirstStart = true;

                    //save
                    Settings.Current.SelectedBuild = build;
                }

                #endregion

                // Localisation
                Core.Current.Info = AppoDataStore.Instance.GetInfoOffline();
                Core.Current.SetupLanguages();

                //setup UI colors etc
                TenantOptions.Init();

                ViewFinderData = new ViewFinderData();

                current = this;

                InitializeComponent();

                System.Net.ServicePointManager.DnsRefreshTimeout = 0;

                InitGlobals();

                Core.Current.Init(this);

                //reboot
                App.Instance.Messager.Subscribe<string>(this, "LoaderOver", async (sender, arg) =>
                {
                    //LEGACy!!!!!

                    return;

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        SetMainPage();
                        SetupScreenOn();
                    });

                    bool pushInitialized = Settings.Current.PushInitialized;

                    Device.StartTimer(TimeSpan.FromSeconds(2), () =>
                    {
                        if (!pushInitialized)
                        {
                            Core.Native.InitPush();
                            Settings.Current.PushInitialized = true;
                        }

                        Core.Current.InitPush();
                        // Don't repeat the timer 

                        //Core.Native.ExecuteTask("showStatusBarImmersive",null);
                        return false;
                    });

                    App.Instance.Messager.All("CloseAllPopups", "");
                });

                App.Instance.Messager.Subscribe<string>(this, "CloseAllPopups", async (sender, arg) =>
                {
                    PopupPage.CloseAllPopups();
                    //Core.Native.ExecuteTask("showStatusBarImmersive");
                });

                App.Instance.Messager.Subscribe<string>(this, "NeedRestart", async (sender, arg) =>
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        var kill = MainPage;

                        Bootstrap();

                        Tasks.StartDelayed(TimeSpan.FromSeconds(3), () =>
                        {
                            if (kill is IDisposable dispose)
                            {
                                dispose.Dispose();
                            }
                        });
                    });
                });

                //legacy
                //SetMainPage(); //MainPage = new ReloadingPage();

                //first run show language selection
                Tasks.StartDelayed(TimeSpan.FromSeconds(2), () =>
                {
                    //Core.Native.ExecuteTask("showStatusBarImmersive");
                    if (Settings.Current.FirstStart)
                    {
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            // Update the UI
                            var changed = await Core.Current.PresentLanguagesSelectionList();
                            if (!changed) return;
                            App.Instance.Messager.All("NeedRestart", "Now");
                        });
                    }

                    Settings.Current.FirstStart = false;
                });

                if (!mainPageOverride)
                {
                    MainThread.BeginInvokeOnMainThread(() => { Bootstrap(); });
                }
                else
                {
                    TenantOptions.Init();
                }

#if xDEBUG
                PreviewFramework.App.Maui.MauiPreviewApplication.Instance.PreviewNavigatorService = new AppMauiPreviewNavigatorService();
#endif

            }
        }

        public static AppFastShell Shell { get; set; }
        public const string NewsUrl = "https://artoffoto.com/category/novosti/";

        protected static HttpClient CreateClient(double timeout = 60, bool withCompression = true)
        {
            var client = new HttpClient(new HttpClientHandler { AutomaticDecompression = DecompressionMethods.GZip, })
            {
                //BaseAddress = new Uri(baseAddress),
                Timeout = TimeSpan.FromSeconds(timeout), DefaultRequestHeaders = { { "User-Agent", "AppoMobi" } },
            };
            if (withCompression)
            {
                client.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue("gzip"));
                client.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue("deflate"));
            }

            return client;
        }

        public static string PrettyJson(object obj)
        {
            //var obj = Newtonsoft.Json.JsonConvert.DeserializeObject(unPrettyJson);
            var ret = Newtonsoft.Json.JsonConvert.SerializeObject(obj, Newtonsoft.Json.Formatting.Indented);
            return ret;
        }

        public class CollectedDeviceInfo
        {
            public string Model => DeviceInfo.Model;
            public string Manufacturer => DeviceInfo.Manufacturer;
            public string Name => DeviceInfo.Name;
            public string VersionString => DeviceInfo.VersionString;
            public Version Version => DeviceInfo.Version;
            public DevicePlatform Platform => DeviceInfo.Platform;
            public DeviceIdiom Idiom => DeviceInfo.Idiom;
            public DeviceType DeviceType => DeviceInfo.DeviceType;
        }

        class CameraInfo
        {
            public string JsonDevice { get; set; }
            public string JsonCamera { get; set; }
        }

        public async Task SaveCameraDevice(CameraUnit cameraDevice)
        {
            var needSend = Settings.Current.GetValueOrDefault("CameraDataSent", false);

            if (needSend)
            {
                try
                {
                    var device = new CollectedDeviceInfo();

                    var dtoFull = new CameraInfo
                    {
                        JsonDevice = JsonConvert.SerializeObject(device),
                        JsonCamera = JsonConvert.SerializeObject(cameraDevice)
                    };

                    var dto = new ReportJsonDto
                    {
                        Context = $"[{device.Model}] {cameraDevice.Id}", Json = JsonConvert.SerializeObject(dtoFull)
                    };

                    var url = "https://appomobi.ru/aof/api/stats/camera";

                    var myClient = CreateClient();

                    StringContent encodedContent = null;
                    if (dto != null)
                    {
                        var send = JsonConvert.SerializeObject(dto);
                        encodedContent = new StringContent(send, Encoding.UTF8, "application/json");
                    }

                    var response = await myClient.PostAsync(url, encodedContent);

                    Debug.WriteLine($"SENT to server: {PrettyJson(dto)}");

                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        throw new ApiException("Status code doesn't indicate success", HttpMethod.Post, url,
                            response.StatusCode);
                    }

                    Settings.Current.AddOrUpdateValue("CameraDataSent", true);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }
        }

        public static void ExecuteOnMainThread(Action action)
        {
            if (MainThread.IsMainThread)
            {
                action();
            }
            else
            {
                MainThread.BeginInvokeOnMainThread(action);
            }
        }

        public static string BuildDesc
        {
            get { return $"Build {VersionTracking.CurrentBuild}"; }
        }

        //public static YandexMetricaConfig AppMetricaConfig()
        //{
        //	return new YandexMetricaConfig("2aeed10e-b302-467e-9894-7f242a39288a")
        //	{
        //		InstalledAppCollecting = true,
        //		CrashReporting = true,
        //		//LocationTracking = true,
        //		Logs = true
        //	};
        //}

        //private static AppoMobi.Services.LoggerYandex _Logger;
        //public static AppoMobi.Services.LoggerYandex Logger
        //{
        //	get
        //	{
        //		if (_Logger == null)
        //		{
        //			_Logger = new LoggerYandex();
        //		}
        //		return _Logger;
        //	}
        //}

        //private static AppoMobi.Services.LoggerYandex _Logger;
        //public static AppoMobi.Services.LoggerYandex Logger
        //{
        //	get
        //	{
        //		if (_Logger == null)
        //		{
        //			_Logger = new LoggerYandex();
        //		}
        //		return _Logger;
        //	}
        //}
        private static AppoMobi.Services.LoggerMock _Logger;

        public static AppoMobi.Services.LoggerMock Logger
        {
            get
            {
                if (_Logger == null)
                {
                    _Logger = new LoggerMock();
                }

                return _Logger;
            }
        }

        static public int ScreenWidth = 0;
        static public int ScreenHeight = 0;

        //public List<MenuPageItem> MenuList;
        public static App current;

        //    public static Preloader Preloader { get; } = new Preloader();
        public static ViewFinderData ViewFinderData { get; set; }

        protected void SetMainPage()
        {
            //we have no bootstrap so:
            /*
            //get core data like enabled languages
            Core.Current.Info = AppoDataStore.Instance.GetInfoOffline();
            Core.Current.SetupLanguages();

            //setup UI colors etc
            TenantOptions.Init();

            //get data localized for correct language
            Core.Current.Info = AppoDataStore.Instance.GetInfoOffline();
            //AuthModel.Instance.LoginOffline();
            */

            TabsAndMenu.Init();

            var children = new List<IView>()
            {
                new ContentPage() { BackgroundColor = Colors.Green },
                new Canvas()
                {
                    BackgroundColor = Colors.Red,
                    HeightRequest = 40,
                    VerticalOptions = LayoutOptions.End,
                    HorizontalOptions = LayoutOptions.Fill,
                    UpdateMode = UpdateMode.Constant
                }
            };

            //var wrap = new WrapperLayout()
            //{
            //    BackgroundColor = Colors.Yellow
            //};
            //wrap.Children.Add(children[0]);
            //wrap.Children.Add(children[1]);

            //var page = new ContentPage()
            //{
            //    Content = wrap
            //};
            //var navi = new NavigationPage(page);
            //MainPage = navi;
            //Core.Current.Navigation = navi.Navigation;

            //var mainPage2 = new RootPageX();
            //MainPage = mainPage2;
            //var navigation2 = mainPage2.Detail.Navigation;
            //Core.Current.Navigation = navigation2;

            //Device.StartTimer(TimeSpan.FromSeconds(1.5), () =>
            //{
            //    App.Instance.Messager.All("MainNavigationReady", "");
            //    // Don't repeat the timer 
            //    return false;
            //});
        }

        public void End()
        {
            //cleanup before disposing
            App.Instance.Messager.Unsubscribe(this, "LoaderOver");
            App.Instance.Messager.Unsubscribe(this, "CloseAllPopups");
            App.Instance.Messager.Unsubscribe(this, "NeedRestart");
        }

        protected override void OnStart()
        {
            // Handle when your app starts
            Core.Current.IsActive = true;
        }

        protected override void OnSleep()
        {
            Core.Current.IsActive = false;

            // Handle when your app sleeps
            MessagingCenter.Send(this, "On", "Sleep");

            Core.Native.ExecuteTask("canSleep");
        }

        protected override void OnResume()
        {
            Core.Current.IsActive = true;
            InitGlobals();
            MessagingCenter.Send(this, "On", "Resume");

            //InitPush();
            //Core.Native.ExecuteTask("showStatusBarImmersive");

            SetupScreenOn();
        }

        void InitGlobals()
        {
            //========================================================
            // Adapt to screen
            //========================================================
            var width = Settings.Current.ResX;
            var height = Settings.Current.ResY;
            double my_height;
            if (height > width)
                my_height = height;
            else
                my_height = width;


            if (Device.Idiom != TargetIdiom.Phone)
            {
                if (DeviceInfo.Current.Platform != DevicePlatform.iOS)
                {
                    Globals.Values.MaxNewsWidth = 600; //tablet?
                }
                else
                {
                    Globals.Values.MaxNewsWidth = 500; //tablet?
                }
            }
            else
            {
                Globals.Values.MaxNewsWidth = 410; //phone
            }

            //defaults
            Globals.Values.SalonImageHeight = 250;
            Globals.Values.SalonFavImageHeight = 300;

            if (DeviceInfo.Current.Platform == DevicePlatform.iOS ||
                DeviceInfo.Current.Platform == DevicePlatform.MacCatalyst)
            {
                Settings.Current.AnimationOff = "yes";
                if (DeviceInfo.Current.Idiom != DeviceIdiom.Phone) //tablet
                {
                    Globals.Values.ProdductsNewsHeight = 80 * 2;
                    Globals.Values.CategoryBannerHeight = AppColors.interface_prodcat_h_Tab;
                    Globals.Values.CategoryBannerHeightSmall = AppColors.interface_prodcat_h_small_Tab;
                    ;
                    if (my_height > 0) // IPAD
                    {
                        Globals.Values.SalonImageHeight = my_height / 100 * 38; //300;
                        Globals.Values.SalonFavImageHeight = my_height / 100 * 41; //300;
                    }
                }
                else
                {
                    Globals.Values.ProdductsNewsHeight = 80;
                    Globals.Values.CategoryBannerHeight = AppColors.interface_prodcat_h;
                    Globals.Values.CategoryBannerHeightSmall = AppColors.interface_prodcat_h_small;
                    ;
                    if (my_height > 0) //iPHONE
                    {
                        Globals.Values.SalonImageHeight = my_height / 100 * 37; //200;
                        Globals.Values.SalonFavImageHeight = my_height / 100 * 40; //300;
                    }
                }
            }
            else
                //if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                if (DeviceInfo.Current.Idiom != DeviceIdiom.Phone) //DROID TAB
                {
                    Globals.Values.ProdductsNewsHeight = 80 * 2;
                    Globals.Values.CategoryBannerHeight = AppColors.interface_prodcat_h_Tab;
                    Globals.Values.CategoryBannerHeightSmall = AppColors.interface_prodcat_h_small_Tab;
                    ;
                    if (my_height > 0)
                    {
                        Globals.Values.SalonImageHeight = my_height / 100 * 38; //300;
                        Globals.Values.SalonFavImageHeight = my_height / 100 * 41; //300;
                    }
                }
                else
                {
                    Globals.Values.ProdductsNewsHeight = 80;
                    Globals.Values.CategoryBannerHeight = AppColors.interface_prodcat_h;
                    Globals.Values.CategoryBannerHeightSmall = AppColors.interface_prodcat_h_small;
                    ;

                    if (my_height > 0) //DROID PHONE
                    {
                        Globals.Values.SalonImageHeight = my_height / 100 * 37; //200;
                        Globals.Values.SalonFavImageHeight = my_height / 100 * 40; //300;
                    }
                }
            }

            //some values from settings
            Globals.Values.iShowTabsText = Settings.Current.iShowTabsText == "yes";
            Globals.Values.AnimationOff = Settings.Current.AnimationOff == "yes";
        }

        public void SetupScreenOn()
        {
            if (Settings.Current.OptionScreenOn == "yes")
            {
                Core.Native.ExecuteTask("cannotSleep");
            }
            else
            {
                Core.Native.ExecuteTask("canSleep");
            }
        }

        public static DateTime GetLinkerTime(Assembly assembly)
        {
            const string BuildVersionMetadataPrefix = "+build";

            var attribute = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>();
            if (attribute?.InformationalVersion != null)
            {
                var value = attribute.InformationalVersion;
                var index = value.IndexOf(BuildVersionMetadataPrefix);
                if (index > 0)
                {
                    value = value.Right(value.Length - (index + BuildVersionMetadataPrefix.Length));
                    return DateTime.ParseExact(value, "yyyy-MM-ddTHH:mm:ss:fffZ", CultureInfo.InvariantCulture);
                }
            }

            return default;
        }

        public static App Instance => App.Current as App;
        public IServiceProvider Services { get; }
        private IInAppMessager _messager;

        public IInAppMessager Messager
        {
            get
            {
                if (_messager == null)
                    _messager = this.Services.GetService<IInAppMessager>();
                return _messager;
            }
        }

        private IUIAssistant _ui;

        public IUIAssistant UI
        {
            get
            {
                if (_ui == null)
                    _ui = this.Services.GetService<IUIAssistant>();
                return _ui;
            }
        }

        private IAppStorage _settings;

        public IAppStorage Storage
        {
            get
            {
                if (_settings == null)
                    _settings = this.Services.GetService<IAppStorage>();
                return _settings;
            }
        }

        private NavigationViewModel _navigationVm;

        public NavigationViewModel Presentation
        {
            get
            {
                if (_navigationVm == null)
                    _navigationVm = this.Services.GetService<NavigationViewModel>();
                return _navigationVm;
            }
        }

        void Bootstrap()
        {
            //setup UI colors etc
            TenantOptions.Init();

            //MainPage = new PageDev();
            //return;

            //FAST SHELL
            Presentation.Initialize(AppRoutes.RootDefault);
        }

        public static void OpenPage(Page page)
        {
            if (App.Instance.MainPage is AppFastShell shell)
            {
                MainThread.BeginInvokeOnMainThread(async () => { await shell.Navigation.PushAsync(page); });
            }
        }
    }
}
