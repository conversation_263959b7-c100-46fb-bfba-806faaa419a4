﻿using AppoMobi.Models;

namespace AppoMobi.Mobile.Views.Camera.Exposure
{
    public static class ExposureData
    {
        /// <summary>
        /// Standard shutter speed values for exposure meter - Full stops only
        /// </summary>
        public static readonly List<ValueItem> ShutterSpeedsFullStop = new List<ValueItem>
        {
            new ValueItem { Title = "1/8000", Value = 1.0 / 8000 },
            new ValueItem { Title = "1/4000", Value = 1.0 / 4000 },
            new ValueItem { Title = "1/2000", Value = 1.0 / 2000 },
            new ValueItem { Title = "1/1000", Value = 1.0 / 1000 },
            new ValueItem { Title = "1/500", Value = 1.0 / 500 },
            new ValueItem { Title = "1/250", Value = 1.0 / 250 },
            new ValueItem { Title = "1/125", Value = 1.0 / 125 },
            new ValueItem { Title = "1/60", Value = 1.0 / 60 },
            new ValueItem { Title = "1/30", Value = 1.0 / 30 },
            new ValueItem { Title = "1/15", Value = 1.0 / 15 },
            new ValueItem { Title = "1/8", Value = 1.0 / 8 },
            new ValueItem { Title = "1/4", Value = 1.0 / 4 },
            new ValueItem { Title = "0.5", Value = 0.5 },
            new ValueItem { Title = "1", Value = 1.0 },
            new ValueItem { Title = "2", Value = 2.0 },
            new ValueItem { Title = "4", Value = 4.0 },
            new ValueItem { Title = "8", Value = 8.0 },
            new ValueItem { Title = "15", Value = 15.0 },
            new ValueItem { Title = "30", Value = 30.0 }
        };

        /// <summary>
        /// Standard shutter speed values for exposure meter - 1/2 stop intervals
        /// </summary>
        public static readonly List<ValueItem> ShutterSpeedsHalfStop = new List<ValueItem>
        {
            new ValueItem { Title = "1/8000", Value = 1.0 / 8000 },
            new ValueItem { Title = "1/5600", Value = 1.0 / 5600 },
            new ValueItem { Title = "1/4000", Value = 1.0 / 4000 },
            new ValueItem { Title = "1/2800", Value = 1.0 / 2800 },
            new ValueItem { Title = "1/2000", Value = 1.0 / 2000 },
            new ValueItem { Title = "1/1400", Value = 1.0 / 1400 },
            new ValueItem { Title = "1/1000", Value = 1.0 / 1000 },
            new ValueItem { Title = "1/700", Value = 1.0 / 700 },
            new ValueItem { Title = "1/500", Value = 1.0 / 500 },
            new ValueItem { Title = "1/350", Value = 1.0 / 350 },
            new ValueItem { Title = "1/250", Value = 1.0 / 250 },
            new ValueItem { Title = "1/180", Value = 1.0 / 180 },
            new ValueItem { Title = "1/125", Value = 1.0 / 125 },
            new ValueItem { Title = "1/90", Value = 1.0 / 90 },
            new ValueItem { Title = "1/60", Value = 1.0 / 60 },
            new ValueItem { Title = "1/45", Value = 1.0 / 45 },
            new ValueItem { Title = "1/30", Value = 1.0 / 30 },
            new ValueItem { Title = "1/20", Value = 1.0 / 20 },
            new ValueItem { Title = "1/15", Value = 1.0 / 15 },
            new ValueItem { Title = "1/10", Value = 1.0 / 10 },
            new ValueItem { Title = "1/8", Value = 1.0 / 8 },
            new ValueItem { Title = "1/6", Value = 1.0 / 6 },
            new ValueItem { Title = "1/4", Value = 1.0 / 4 },
            new ValueItem { Title = "0.3", Value = 0.3 },
            new ValueItem { Title = "0.5", Value = 0.5 },
            new ValueItem { Title = "0.7", Value = 0.7 },
            new ValueItem { Title = "1", Value = 1.0 },
            new ValueItem { Title = "1.5", Value = 1.5 },
            new ValueItem { Title = "2", Value = 2.0 },
            new ValueItem { Title = "3", Value = 3.0 },
            new ValueItem { Title = "4", Value = 4.0 },
            new ValueItem { Title = "6", Value = 6.0 },
            new ValueItem { Title = "8", Value = 8.0 },
            new ValueItem { Title = "10", Value = 10.0 },
            new ValueItem { Title = "15", Value = 15.0 },
            new ValueItem { Title = "20", Value = 20.0 },
            new ValueItem { Title = "30", Value = 30.0 }
        };

        /// <summary>
        /// Standard shutter speed values for exposure meter - 1/3 stop intervals (most common)
        /// </summary>
        public static readonly List<ValueItem> ShutterSpeedsThirdStop = new List<ValueItem>
        {
            new ValueItem { Title = "1/8000", Value = 1.0 / 8000 },
            new ValueItem { Title = "1/6400", Value = 1.0 / 6400 },
            new ValueItem { Title = "1/5000", Value = 1.0 / 5000 },
            new ValueItem { Title = "1/4000", Value = 1.0 / 4000 },
            new ValueItem { Title = "1/3200", Value = 1.0 / 3200 },
            new ValueItem { Title = "1/2500", Value = 1.0 / 2500 },
            new ValueItem { Title = "1/2000", Value = 1.0 / 2000 },
            new ValueItem { Title = "1/1600", Value = 1.0 / 1600 },
            new ValueItem { Title = "1/1250", Value = 1.0 / 1250 },
            new ValueItem { Title = "1/1000", Value = 1.0 / 1000 },
            new ValueItem { Title = "1/800", Value = 1.0 / 800 },
            new ValueItem { Title = "1/640", Value = 1.0 / 640 },
            new ValueItem { Title = "1/500", Value = 1.0 / 500 },
            new ValueItem { Title = "1/400", Value = 1.0 / 400 },
            new ValueItem { Title = "1/320", Value = 1.0 / 320 },
            new ValueItem { Title = "1/250", Value = 1.0 / 250 },
            new ValueItem { Title = "1/200", Value = 1.0 / 200 },
            new ValueItem { Title = "1/160", Value = 1.0 / 160 },
            new ValueItem { Title = "1/125", Value = 1.0 / 125 },
            new ValueItem { Title = "1/100", Value = 1.0 / 100 },
            new ValueItem { Title = "1/80", Value = 1.0 / 80 },
            new ValueItem { Title = "1/60", Value = 1.0 / 60 },
            new ValueItem { Title = "1/50", Value = 1.0 / 50 },
            new ValueItem { Title = "1/40", Value = 1.0 / 40 },
            new ValueItem { Title = "1/30", Value = 1.0 / 30 },
            new ValueItem { Title = "1/25", Value = 1.0 / 25 },
            new ValueItem { Title = "1/20", Value = 1.0 / 20 },
            new ValueItem { Title = "1/15", Value = 1.0 / 15 },
            new ValueItem { Title = "1/13", Value = 1.0 / 13 },
            new ValueItem { Title = "1/10", Value = 1.0 / 10 },
            new ValueItem { Title = "1/8", Value = 1.0 / 8 },
            new ValueItem { Title = "1/6", Value = 1.0 / 6 },
            new ValueItem { Title = "1/5", Value = 1.0 / 5 },
            new ValueItem { Title = "1/4", Value = 1.0 / 4 },
            new ValueItem { Title = "0.3", Value = 0.3 },
            new ValueItem { Title = "0.4", Value = 0.4 },
            new ValueItem { Title = "0.5", Value = 0.5 },
            new ValueItem { Title = "0.6", Value = 0.6 },
            new ValueItem { Title = "0.8", Value = 0.8 },
            new ValueItem { Title = "1", Value = 1.0 },
            new ValueItem { Title = "1.3", Value = 1.3 },
            new ValueItem { Title = "1.6", Value = 1.6 },
            new ValueItem { Title = "2", Value = 2.0 },
            new ValueItem { Title = "2.5", Value = 2.5 },
            new ValueItem { Title = "3", Value = 3.0 },
            new ValueItem { Title = "4", Value = 4.0 },
            new ValueItem { Title = "5", Value = 5.0 },
            new ValueItem { Title = "6", Value = 6.0 },
            new ValueItem { Title = "8", Value = 8.0 },
            new ValueItem { Title = "10", Value = 10.0 },
            new ValueItem { Title = "13", Value = 13.0 },
            new ValueItem { Title = "15", Value = 15.0 },
            new ValueItem { Title = "20", Value = 20.0 },
            new ValueItem { Title = "25", Value = 25.0 },
            new ValueItem { Title = "30", Value = 30.0 }
        };

        /// <summary>
        /// Default shutter speed values - currently set to 1/3 stop intervals (most common in modern cameras)
        /// This property can be changed to use different stop intervals based on user preference
        /// </summary>
        public static List<ValueItem> ShutterSpeeds => ShutterSpeedsThirdStop;

        /// <summary>
        /// Standard ISO values for exposure meter - Full stops only
        /// </summary>
        public static readonly List<ValueItem> IsoValuesFullStop = new List<ValueItem>
        {
            new ValueItem { Title = "50", Value = 50 },
            new ValueItem { Title = "100", Value = 100 },
            new ValueItem { Title = "200", Value = 200 },
            new ValueItem { Title = "400", Value = 400 },
            new ValueItem { Title = "800", Value = 800 },
            new ValueItem { Title = "1600", Value = 1600 },
            new ValueItem { Title = "3200", Value = 3200 },
            new ValueItem { Title = "6400", Value = 6400 },
            new ValueItem { Title = "12800", Value = 12800 },
            new ValueItem { Title = "25600", Value = 25600 },
            new ValueItem { Title = "51200", Value = 51200 },
            new ValueItem { Title = "102400", Value = 102400 }
        };

        /// <summary>
        /// Standard ISO values for exposure meter - 1/2 stop intervals
        /// </summary>
        public static readonly List<ValueItem> IsoValuesHalfStop = new List<ValueItem>
        {
            new ValueItem { Title = "50", Value = 50 },
            new ValueItem { Title = "70", Value = 70 },
            new ValueItem { Title = "100", Value = 100 },
            new ValueItem { Title = "140", Value = 140 },
            new ValueItem { Title = "200", Value = 200 },
            new ValueItem { Title = "280", Value = 280 },
            new ValueItem { Title = "400", Value = 400 },
            new ValueItem { Title = "560", Value = 560 },
            new ValueItem { Title = "800", Value = 800 },
            new ValueItem { Title = "1100", Value = 1100 },
            new ValueItem { Title = "1600", Value = 1600 },
            new ValueItem { Title = "2200", Value = 2200 },
            new ValueItem { Title = "3200", Value = 3200 },
            new ValueItem { Title = "4500", Value = 4500 },
            new ValueItem { Title = "6400", Value = 6400 },
            new ValueItem { Title = "9000", Value = 9000 },
            new ValueItem { Title = "12800", Value = 12800 },
            new ValueItem { Title = "18000", Value = 18000 },
            new ValueItem { Title = "25600", Value = 25600 },
            new ValueItem { Title = "36000", Value = 36000 },
            new ValueItem { Title = "51200", Value = 51200 },
            new ValueItem { Title = "72000", Value = 72000 },
            new ValueItem { Title = "102400", Value = 102400 }
        };

        /// <summary>
        /// Standard ISO values for exposure meter - 1/3 stop intervals (most common)
        /// </summary>
        public static readonly List<ValueItem> IsoValuesThirdStop = new List<ValueItem>
        {
            new ValueItem { Title = "50", Value = 50 },
            new ValueItem { Title = "64", Value = 64 },
            new ValueItem { Title = "80", Value = 80 },
            new ValueItem { Title = "100", Value = 100 },
            new ValueItem { Title = "125", Value = 125 },
            new ValueItem { Title = "160", Value = 160 },
            new ValueItem { Title = "200", Value = 200 },
            new ValueItem { Title = "250", Value = 250 },
            new ValueItem { Title = "320", Value = 320 },
            new ValueItem { Title = "400", Value = 400 },
            new ValueItem { Title = "500", Value = 500 },
            new ValueItem { Title = "640", Value = 640 },
            new ValueItem { Title = "800", Value = 800 },
            new ValueItem { Title = "1000", Value = 1000 },
            new ValueItem { Title = "1250", Value = 1250 },
            new ValueItem { Title = "1600", Value = 1600 },
            new ValueItem { Title = "2000", Value = 2000 },
            new ValueItem { Title = "2500", Value = 2500 },
            new ValueItem { Title = "3200", Value = 3200 },
            new ValueItem { Title = "4000", Value = 4000 },
            new ValueItem { Title = "5000", Value = 5000 },
            new ValueItem { Title = "6400", Value = 6400 },
            new ValueItem { Title = "8000", Value = 8000 },
            new ValueItem { Title = "10000", Value = 10000 },
            new ValueItem { Title = "12800", Value = 12800 },
            new ValueItem { Title = "16000", Value = 16000 },
            new ValueItem { Title = "20000", Value = 20000 },
            new ValueItem { Title = "25600", Value = 25600 },
            new ValueItem { Title = "32000", Value = 32000 },
            new ValueItem { Title = "40000", Value = 40000 },
            new ValueItem { Title = "51200", Value = 51200 },
            new ValueItem { Title = "64000", Value = 64000 },
            new ValueItem { Title = "80000", Value = 80000 },
            new ValueItem { Title = "102400", Value = 102400 }
        };

        /// <summary>
        /// Default ISO values - currently set to 1/3 stop intervals (most common in modern cameras)
        /// This property can be changed to use different stop intervals based on user preference
        /// </summary>
        public static List<ValueItem> IsoValues => IsoValuesThirdStop;

        /// <summary>
        /// Standard aperture (f-stop) values for exposure meter - Full stops only
        /// </summary>
        public static readonly List<ValueItem> ApertureValuesFullStop = new List<ValueItem>
        {
            new ValueItem { Title = "f/1.0", Value = 1.0 },
            new ValueItem { Title = "f/1.4", Value = 1.4 },
            new ValueItem { Title = "f/2.0", Value = 2.0 },
            new ValueItem { Title = "f/2.8", Value = 2.8 },
            new ValueItem { Title = "f/4.0", Value = 4.0 },
            new ValueItem { Title = "f/5.6", Value = 5.6 },
            new ValueItem { Title = "f/8.0", Value = 8.0 },
            new ValueItem { Title = "f/11", Value = 11.0 },
            new ValueItem { Title = "f/16", Value = 16.0 },
            new ValueItem { Title = "f/22", Value = 22.0 },
            new ValueItem { Title = "f/32", Value = 32.0 },
            new ValueItem { Title = "f/45", Value = 45.0 },
            new ValueItem { Title = "f/64", Value = 64.0 }
        };

        /// <summary>
        /// Standard aperture (f-stop) values for exposure meter - 1/2 stop intervals
        /// </summary>
        public static readonly List<ValueItem> ApertureValuesHalfStop = new List<ValueItem>
        {
            new ValueItem { Title = "f/1.0", Value = 1.0 },
            new ValueItem { Title = "f/1.2", Value = 1.2 },
            new ValueItem { Title = "f/1.4", Value = 1.4 },
            new ValueItem { Title = "f/1.7", Value = 1.7 },
            new ValueItem { Title = "f/2.0", Value = 2.0 },
            new ValueItem { Title = "f/2.4", Value = 2.4 },
            new ValueItem { Title = "f/2.8", Value = 2.8 },
            new ValueItem { Title = "f/3.3", Value = 3.3 },
            new ValueItem { Title = "f/4.0", Value = 4.0 },
            new ValueItem { Title = "f/4.8", Value = 4.8 },
            new ValueItem { Title = "f/5.6", Value = 5.6 },
            new ValueItem { Title = "f/6.7", Value = 6.7 },
            new ValueItem { Title = "f/8.0", Value = 8.0 },
            new ValueItem { Title = "f/9.5", Value = 9.5 },
            new ValueItem { Title = "f/11", Value = 11.0 },
            new ValueItem { Title = "f/13", Value = 13.0 },
            new ValueItem { Title = "f/16", Value = 16.0 },
            new ValueItem { Title = "f/19", Value = 19.0 },
            new ValueItem { Title = "f/22", Value = 22.0 },
            new ValueItem { Title = "f/27", Value = 27.0 },
            new ValueItem { Title = "f/32", Value = 32.0 },
            new ValueItem { Title = "f/38", Value = 38.0 },
            new ValueItem { Title = "f/45", Value = 45.0 },
            new ValueItem { Title = "f/54", Value = 54.0 },
            new ValueItem { Title = "f/64", Value = 64.0 }
        };

        /// <summary>
        /// Standard aperture (f-stop) values for exposure meter - 1/3 stop intervals (most common)
        /// </summary>
        public static readonly List<ValueItem> ApertureValuesThirdStop = new List<ValueItem>
        {
            new ValueItem { Title = "f/1.0", Value = 1.0 },
            new ValueItem { Title = "f/1.1", Value = 1.1 },
            new ValueItem { Title = "f/1.2", Value = 1.2 },
            new ValueItem { Title = "f/1.4", Value = 1.4 },
            new ValueItem { Title = "f/1.6", Value = 1.6 },
            new ValueItem { Title = "f/1.8", Value = 1.8 },
            new ValueItem { Title = "f/2.0", Value = 2.0 },
            new ValueItem { Title = "f/2.2", Value = 2.2 },
            new ValueItem { Title = "f/2.5", Value = 2.5 },
            new ValueItem { Title = "f/2.8", Value = 2.8 },
            new ValueItem { Title = "f/3.2", Value = 3.2 },
            new ValueItem { Title = "f/3.5", Value = 3.5 },
            new ValueItem { Title = "f/4.0", Value = 4.0 },
            new ValueItem { Title = "f/4.5", Value = 4.5 },
            new ValueItem { Title = "f/5.0", Value = 5.0 },
            new ValueItem { Title = "f/5.6", Value = 5.6 },
            new ValueItem { Title = "f/6.3", Value = 6.3 },
            new ValueItem { Title = "f/7.1", Value = 7.1 },
            new ValueItem { Title = "f/8.0", Value = 8.0 },
            new ValueItem { Title = "f/9.0", Value = 9.0 },
            new ValueItem { Title = "f/10", Value = 10.0 },
            new ValueItem { Title = "f/11", Value = 11.0 },
            new ValueItem { Title = "f/13", Value = 13.0 },
            new ValueItem { Title = "f/14", Value = 14.0 },
            new ValueItem { Title = "f/16", Value = 16.0 },
            new ValueItem { Title = "f/18", Value = 18.0 },
            new ValueItem { Title = "f/20", Value = 20.0 },
            new ValueItem { Title = "f/22", Value = 22.0 },
            new ValueItem { Title = "f/25", Value = 25.0 },
            new ValueItem { Title = "f/29", Value = 29.0 },
            new ValueItem { Title = "f/32", Value = 32.0 },
            new ValueItem { Title = "f/36", Value = 36.0 },
            new ValueItem { Title = "f/40", Value = 40.0 },
            new ValueItem { Title = "f/45", Value = 45.0 },
            new ValueItem { Title = "f/51", Value = 51.0 },
            new ValueItem { Title = "f/57", Value = 57.0 },
            new ValueItem { Title = "f/64", Value = 64.0 }
        };

        /// <summary>
        /// Default aperture values - currently set to 1/3 stop intervals (most common in modern cameras)
        /// This property can be changed to use different stop intervals based on user preference
        /// </summary>
        public static List<ValueItem> ApertureValues => ApertureValuesThirdStop;

        /// <summary>
        /// Enumeration for stop intervals (used for aperture, shutter speed, and ISO)
        /// </summary>
        public enum StopInterval
        {
            FullStop,
            HalfStop,
            ThirdStop
        }

        /// <summary>
        /// Gets aperture values for the specified stop interval
        /// </summary>
        /// <param name="interval">The desired stop interval</param>
        /// <returns>List of aperture values for the specified interval</returns>
        public static List<ValueItem> GetApertureValues(StopInterval interval)
        {
            return interval switch
            {
                StopInterval.FullStop => ApertureValuesFullStop,
                StopInterval.HalfStop => ApertureValuesHalfStop,
                StopInterval.ThirdStop => ApertureValuesThirdStop,
                _ => ApertureValuesThirdStop
            };
        }

        /// <summary>
        /// Gets shutter speed values for the specified stop interval
        /// </summary>
        /// <param name="interval">The desired stop interval</param>
        /// <returns>List of shutter speed values for the specified interval</returns>
        public static List<ValueItem> GetShutterSpeedValues(StopInterval interval)
        {
            return interval switch
            {
                StopInterval.FullStop => ShutterSpeedsFullStop,
                StopInterval.HalfStop => ShutterSpeedsHalfStop,
                StopInterval.ThirdStop => ShutterSpeedsThirdStop,
                _ => ShutterSpeedsThirdStop
            };
        }

        /// <summary>
        /// Gets ISO values for the specified stop interval
        /// </summary>
        /// <param name="interval">The desired stop interval</param>
        /// <returns>List of ISO values for the specified interval</returns>
        public static List<ValueItem> GetIsoValues(StopInterval interval)
        {
            return interval switch
            {
                StopInterval.FullStop => IsoValuesFullStop,
                StopInterval.HalfStop => IsoValuesHalfStop,
                StopInterval.ThirdStop => IsoValuesThirdStop,
                _ => IsoValuesThirdStop
            };
        }
    }
}
