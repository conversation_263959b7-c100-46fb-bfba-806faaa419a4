﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
        <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
        
        <ApplicationId>com.drawnioui.repro</ApplicationId>
        <ApplicationIdGuid>330e66b0-efc2-4fde-9ac8-074d5c3299b3</ApplicationIdGuid>
        <ApplicationTitle>Sandbox v3</ApplicationTitle>
        <OutputType>Exe</OutputType>
        <RootNamespace>Sandbox</RootNamespace>
        <UseMaui>true</UseMaui>
        <SingleProject>true</SingleProject>
        <ImplicitUsings>enable</ImplicitUsings>

    </PropertyGroup>

	<PropertyGroup>
        <WindowsPackageType>MSIX</WindowsPackageType>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
        <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
        <AssemblyName>$(MSBuildProjectName)</AssemblyName>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net*-ios|AnyCPU'">
	  <CreatePackage>false</CreatePackage>
        <CodesignKey>iPhone Developer</CodesignKey>
	</PropertyGroup>

  <ItemGroup>
	  <MauiImage Include="Resources\Raw\Svg\dotnet_bot.svg">
	    <BaseSize>168,208</BaseSize>
	  </MauiImage>
	</ItemGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <!--<EnableLLVM>True</EnableLLVM>-->
    <Optimize>True</Optimize>
    <!--<RunAOTCompilation>True</RunAOTCompilation>
    <PublishTrimmed>True</PublishTrimmed>
    <MtouchUseLlvm>True</MtouchUseLlvm>-->
    <AndroidEnableSGenConcurrent>True</AndroidEnableSGenConcurrent>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net*-android|AnyCPU'">
      <EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net*-android|AnyCPU'">
    <AndroidEnableMultiDex>True</AndroidEnableMultiDex>
  </PropertyGroup>
  
  <PropertyGroup Condition="$(TargetFramework.Contains('ios')) == true">
    <ProvisioningType>manual</ProvisioningType>
  </PropertyGroup>


  <ItemGroup>

    <None Remove="Resources\Fonts\Orbitron-Black.ttf" />

    <None Remove="Resources\Fonts\Orbitron-Bold.ttf" />

    <None Remove="Resources\Fonts\Orbitron-ExtraBold.ttf" />

    <None Remove="Resources\Fonts\Orbitron-Medium.ttf" />

    <None Remove="Resources\Fonts\Orbitron-Regular.ttf" />

    <None Remove="Resources\Fonts\Orbitron-SemiBold.ttf" />

    <None Remove="Resources\Images\" />

    <None Remove="Resources\Raw\baboon.jpg" />

    <None Remove="Resources\Raw\dotnet_bot.png" />

    <None Remove="Resources\Raw\Images\8.jpg" />

    <None Remove="Resources\Raw\Lottie\cross.json" />

    <None Remove="Resources\Raw\Lottie\Loader.json" />

    <None Remove="Resources\Raw\Lottie\robot.json" />

    <None Remove="Resources\Raw\Shaders\invert.sksl" />

    <None Remove="Resources\Raw\Shaders\transdoorway.sksl" />

    <None Remove="Resources\Raw\Shaders\transfade.sksl" />

    <None Remove="Resources\Raw\Svg\dotnet_bot.svg" />

    <!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

    <!-- Splash Screen -->
    <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<!--<MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />-->

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

    <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
    <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
    <None Remove="Models\**" />
	</ItemGroup>


	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
	</ItemGroup>


	<ItemGroup>
	  <ProjectReference Include="..\..\DrawnUi\DrawnUi.Maui.csproj" />
	</ItemGroup>

    <ItemGroup>
 
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
 
    <EmbeddedResource Update="Resources\Strings\ResStrings.resx">
        <Generator>PublicResXFileCodeGenerator</Generator>
        <LastGenOutput>ResStrings.Designer.cs</LastGenOutput>
      </EmbeddedResource>
    
    </ItemGroup>


  <ItemGroup>
    <MauiAsset Update="Resources\Raw\Lottie\cross.json">
      <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
    </MauiAsset>
    <MauiAsset Update="Resources\Raw\Lottie\Loader.json">
      <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
    </MauiAsset>
    <MauiAsset Update="Resources\Raw\Lottie\robot.json">
      <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
    </MauiAsset>
    <MauiAsset Update="Resources\Raw\Shaders\transfade.sksl">
      <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
    </MauiAsset>
    <MauiAsset Update="Resources\Raw\Shaders\transdoorway.sksl">
      <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
    </MauiAsset>
    <MauiAsset Update="Resources\Raw\Svg\dotnet_bot.svg">
      <LogicalName>%(RecursiveDir)%(Filename)%(Extension)</LogicalName>
    </MauiAsset>
  </ItemGroup>


  <ItemGroup>
    <MauiXaml Update="DrawnRadioButton.xaml">
      <Generator>MSBuild:Compile</Generator>
    </MauiXaml>
    <MauiXaml Update="TutorialCards.xaml">
      <Generator>MSBuild:Compile</Generator>
    </MauiXaml>
  </ItemGroup>


  <ItemGroup>
    <Folder Include="Resources\Raw\Anims\" />
    <Folder Include="Resources\Raw\Json\" />
    <Folder Include="Resources\Raw\Lottie\" />
  </ItemGroup>


  <ItemGroup>
    <Compile Update="TutorialCards.xaml.cs">
      <DependentUpon>TutorialCards.xaml</DependentUpon>
    </Compile>
  </ItemGroup>

 


 
</Project>
 