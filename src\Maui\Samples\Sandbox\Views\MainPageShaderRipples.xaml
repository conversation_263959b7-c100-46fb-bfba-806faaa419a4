﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="Sandbox.Views.MainPageShaderRipples"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Sandbox.Views.Controls"
    xmlns:demo="clr-namespace:Sandbox"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:views="clr-namespace:Sandbox.Views"
    x:Name="ThisPage"
    x:DataType="demo:MainPageViewModel">

    <draw:Canvas
        Gestures="Enabled"
        HorizontalOptions="Fill"
        RenderingMode="Accelerated"
        Tag="MainPage"
        VerticalOptions="Fill">


        <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaControl.VisualEffects>

                    <!--<draw:ChainColorPresetEffect Preset="BlackAndWhite" />-->

                    <controls:MultiRippleWithTouchEffect SecondarySource="Images/maui1.png" />

                </draw:SkiaControl.VisualEffects>

                <draw:SkiaScroll
                    BackgroundColor="Black"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">


                    <draw:SkiaLayout
                        BackgroundColor="Blue"
                        HorizontalOptions="Fill"
                        Tag="Content"
                        Type="Column">

                        <draw:SkiaLayout HeightRequest="350" HorizontalOptions="Fill">

                            <draw:SkiaLayout
                                BackgroundColor="Black"
                                HorizontalOptions="Fill"
                                Tag="Front"
                                Type="Column"
                                VerticalOptions="Fill">

                                <draw:SkiaImage
                                    Aspect="AspectCover"
                                    HeightRequest="250"
                                    HorizontalOptions="Fill"
                                    LoadSourceOnFirstDraw="False"
                                    Source="Images/8.jpg"
                                    UseCache="Image " />

                                <draw:SkiaRichLabel
                                    Margin="16,0"
                                    FontFamily="FontText"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="Infinite ripples with reflection! 😋 _#dotnetmaui_"
                                    TextColor="White"
                                    UseCache="Image" />

                            </draw:SkiaLayout>
                        </draw:SkiaLayout>
                    </draw:SkiaLayout>

                </draw:SkiaScroll>



            </draw:SkiaLayout>


            <controls:ButtonToRoot />

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>

</views:BasePageCodeBehind>
