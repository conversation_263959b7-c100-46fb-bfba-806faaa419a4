using AppoMobi.Main;
using AppoMobi.Mobile.Import.Common.ResX;
using DrawnUi.Views;
using System.Windows.Input;
using AppoMobi.Mobile.Views.Popups;

namespace AppoMobi.Xam
{
    /// <summary>
    /// DrawnUI version of PopupEditCorrection.
    ///
    /// Usage example (replace PopupEditCorrection with PopupEditCorrectionDrawn):
    ///
    /// // Original usage:
    /// // var dialog = new PopupEditCorrection(new Command((object context) => {
    /// //     var value = (double)context;
    /// //     Correction = value;
    /// // }), ResStrings.Correction, Correction);
    /// // dialog.Open();
    ///
    /// // New drawn usage:
    /// PopupEditCorrectionDrawn.Show(new Command((object context) => {
    ///     var value = (double)context;
    ///     Correction = value;
    /// }), ResStrings.Correction, Correction);
    ///
    /// </summary>
    public class PopupEditCorrectionDrawn : AppoMobi.Main.AppScreen
    {
        private SkiaRichLabel _titleLabel;
        private SkiaLabel _valueLabel;
        private SkiaHotspot _minusButton;
        private SkiaHotspot _plusButton;
        private SkiaShape _okButton;

        public ICommand CallbackCommand { get; set; }

        private double _data;

        public double Data
        {
            get { return _data; }
            set
            {
                if (_data != value)
                {
                    _data = value;
                    UpdateValueDisplay();
                }
            }
        }

        private string _title;

        public string Title
        {
            get { return _title; }
            set
            {
                if (_title != value)
                {
                    _title = value;
                    UpdateTitleDisplay();
                }
            }
        }

        public PopupEditCorrectionDrawn(ICommand callback, string title, double defaultValue)
        {
            CallbackCommand = callback;
            Title = title;
            Data = defaultValue;

            Tapped += (sender, args) => { Dismiss(); };

            VerticalOptions = LayoutOptions.Fill;
            HorizontalOptions = LayoutOptions.Fill;

            //ColumnDefinitions = new ColumnDefinitionCollection()
            //{
            //    new ColumnDefinition(GridLength.Star),
            //    new ColumnDefinition(new GridLength(100, GridUnitType.Absolute)),
            //    new ColumnDefinition(GridLength.Auto),
            //};
            //ColumnSpacing = 0;
            //Type = LayoutType.Grid;

            CreateContent();
        }

        private void CreateContent()
        {
            Children = new List<SkiaControl>()
            {
                new SkiaLayout()
                {
                    Margin = new(0, 40, 0, 40),
                    //WidthRequest = 300,
                    HorizontalOptions = LayoutOptions.End,
                    VerticalOptions = LayoutOptions.Center,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaLayout()
                        {
                            Tag = "Debug",
                            Type = LayoutType.Column,
                            Spacing = 0,
                            BackgroundColor = BackColors.OptionLine,
                            Children =  
                            {
                                // HEADER
                                new SkiaLayout
                                {
                                    UseCache = SkiaCacheType.Image,
                                    Tag = "A",
                                    Type = LayoutType.Absolute,
                                    HorizontalOptions = LayoutOptions.Fill,
                                    VerticalOptions = LayoutOptions.Start,
                                    FillGradient =
                                        new SkiaGradient()
                                        {
                                            StartXRatio = 1,
                                            EndXRatio = 0,
                                            StartYRatio = 0,
                                            EndYRatio = 0,
                                            Colors =
                                                new Color[] { BackColors.GradientStartNav, BackColors.GradientEndNav }
                                        },
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaControl() { BackgroundColor = Color.Parse("#33000000"), }.Fill(),
                                        new SkiaRichLabel()
                                        {
                                            FontFamily = "ui",
                                            FontSize = 14,
                                            Text = "header",
                                            Margin = new Thickness(20, 8, 8, 8),
                                            TextColor = Colors.WhiteSmoke,
                                            VerticalTextAlignment = TextAlignment.Center
                                        }.Assign(out _titleLabel)
                                    }
                                },

                                //CONTROLS
                                new SkiaLayout
                                {
                                    Tag = "B",
                                    Type = LayoutType.Row,
                                    Spacing = 10,
                                    Margin = new Thickness(16),
                                    Children = new List<SkiaControl>()
                                    {
                                        Elements.CreateButton("-").WithWidth(32)
                                            .OnTapped(me =>
                                            {
                                                if (Data > -10)
                                                    Data -= 0.5;
                                            }),
                                        new SkiaShape
                                        {
                                            BackgroundColor = Colors.Black,
                                            CornerRadius = 8,
                                            WidthRequest = 80,
                                            VerticalOptions = LayoutOptions.Center,
                                            Padding = 1,
                                            Children = new List<SkiaControl>()
                                            {
                                                new SkiaLabel()
                                                {
                                                    Margin = 8,
                                                    FontSize = 14,
                                                    Format = "{0} EV",
                                                    FontFamily = "FontText",
                                                    TextColor = Colors.White,
                                                    HorizontalOptions = LayoutOptions.Center,
                                                    VerticalOptions = LayoutOptions.Center
                                                }.Assign(out _valueLabel)
                                            }
                                        },
                                        Elements.CreateButton(@"+").WithWidth(32)
                                            .OnTapped(me =>
                                            {
                                                if (Data < 10)
                                                    Data += 0.5;
                                            }),
                                    }
                                },

                                // OK
                                Elements.CreateButton(ResStrings.BtnOk)
                                    .FillX()
                                    .WithMargin(16, 0, 16, 16)
                                    .OnTapped(me =>
                                    {
                                        //wonna see ripple on the button before closing
                                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(150), () =>
                                        {
                                            CallbackCommand?.Execute(Data);
                                            Dismiss();
                                        });
                                    })
                            }
                        }
                    }
                }.WithColumn(2)
            };

            UpdateTitleDisplay();
            UpdateValueDisplay();
        }

        public void Dismiss()
        {
            PopupPage.CloseAllPopups();
        }

        private void UpdateTitleDisplay()
        {
            if (_titleLabel != null)
            {
                _titleLabel.Text = Title ?? "";
            }
        }

        private void UpdateValueDisplay()
        {
            if (_valueLabel != null)
            {
                _valueLabel.Text = $"{Data:0.0}";
            }
        }

        public static async Task<double?> ShowAsync(string title, double defaultValue, ICommand callback = null)
        {
            var tcs = new TaskCompletionSource<double?>();

            var popupContent = new PopupEditCorrectionDrawn(
                new Command<double>(result =>
                {
                    tcs.SetResult(result);
                    callback?.Execute(result);
                }),
                title,
                defaultValue);

            var popup = new CustomPopup()
            {
                Content = new ContentView()
                {
                    Content = new ScreenCanvas()
                    {
                        Content = popupContent,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.Fill
                    }
                },
                HorizontalOptions = Microsoft.Maui.Primitives.LayoutAlignment.End,
                VerticalOptions = Microsoft.Maui.Primitives.LayoutAlignment.Center
            };

            popup.Open(true);

            var result = await tcs.Task;
            popup.Dismiss();

            return result;
        }

        /// <summary>
        /// Shows the popup and returns immediately. Use this method to match the original PopupEditCorrection usage pattern.
        /// </summary>
        /// <param name="callback">Command to execute when OK is pressed</param>
        /// <param name="title">Title text</param>
        /// <param name="defaultValue">Initial value</param>
        public static void Show(ICommand callback, string title, double defaultValue)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var popupContent = new PopupEditCorrectionDrawn(callback, title, defaultValue);

                var popup = new CustomPopup()
                {
                    Content = new ContentView()
                    {
                        Content = new ScreenCanvas() { Content = popupContent }
                    },
                };

                popup.Open(true);
            });
        }
    }
}
