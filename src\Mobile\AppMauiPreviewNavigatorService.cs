﻿#if xDEBUG

using System;
using System.Threading.Tasks;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Controls;
using PreviewFramework;
using PreviewFramework.App.Maui;
using PreviewFramework.SharedModel;
using PreviewFramework.SharedModel.App;

namespace AppoMobi.Mobile
{
    public class AppMauiPreviewNavigatorService : MauiPreviewNavigatorService
    {
        public override async Task NavigateToPreviewAsync(UIComponentReflection uiComponent, PreviewReflection preview)
        {
            var navigateFrom = App.Shell;

            //Application.Current!.MainPage!.Navigation;

            await MainThread.InvokeOnMainThreadAsync(async () =>
            {
                object? previewUI = preview.Create();

                if (uiComponent.Kind == UIComponentKind.Control)
                {
                    ContentPage controlsPage = new ContentPage
                    {
                        Content = (View)previewUI
                    };

                    await navigateFrom.PushAsync(controlsPage, NavigateAnimationsEnabled);
                }
                else
                {
                    if (previewUI is RoutePreview routePreview)
                    {
                        Window? mainWindow = Application.Current!.Windows[0];
                        Shell? shell = mainWindow?.Page as Shell;

          

                        if (shell is null)
                        {
                            throw new InvalidOperationException("Main window doesn't use Shell");
                        }

                        await shell.GoToAsync(routePreview.Route, NavigateAnimationsEnabled);
                    }
                    else if (previewUI is ContentPage contentPage)
                    {
                        //MauiPreviewApplication.Instance.Application.MainPage = contentPage;
                        await navigateFrom.PushAsync(contentPage, NavigateAnimationsEnabled);
                    }
                }
            });
        }
    }
}


#endif
