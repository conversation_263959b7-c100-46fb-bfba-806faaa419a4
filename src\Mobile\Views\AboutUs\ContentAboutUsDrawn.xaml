<?xml version="1.0" encoding="UTF-8"?>

<pages:IncludedContent
    x:Class="AppoMobi.ContentAboutUsDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:nifty="clr-namespace:AppoMobi.Nifty"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:views="clr-namespace:AppoMobi.Mobile.Views"
    xmlns:xam="clr-namespace:AppoMobi.Xam">


    <draw:Canvas
        Gestures="Lock"
        HorizontalOptions="Fill"
        RenderingMode="Accelerated"
        VerticalOptions="Fill">

        <draw:SkiaScroll HorizontalOptions="Fill" VerticalOptions="Fill">

            <draw:SkiaStack
                Padding="2,0,2,0"
                UseCache="Image"
                Spacing="0">

                <!--  CARD 1  -->
                <draw:SkiaShape
                    BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                    CornerRadius="8"
                    Margin="4"
                    Type="Rectangle">

                    <draw:SkiaLayout
                        Margin="0,5,0,8"
                        Spacing="0"
                        Type="Column">

                        <!--  image  -->
                        <draw:SkiaImage
                            x:Name="imgLogo"
                            UseCache="Image"
                            Margin="4,4,4,4"
                            Aspect="AspectFit"
                            RescalingQuality="High"
                            Source="Images\logoabout.jpg" />

                        <!--  Icons will be added programmatically  -->
                        <draw:SkiaStack
                            x:Name="IconsList"
                            Margin="12,0,12,12"
                            BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                            Spacing="2"/>

                        <!--  Description Label  -->
                        <draw:SkiaRichLabel
                            UseCache="Operations"
                            x:Name="cCollapsedLabel"
                            ParagraphSpacing="0.5"
                            Margin="12,0,12,8"
                            LineSpacing="1.1"
                            Text="{x:Static resX:ResStrings.OfflineCompanyDesc}"
                            TextColor="{x:Static appoMobi:AppColors.CardsHeaderText}" />

                    </draw:SkiaLayout>
                </draw:SkiaShape>

                <!--  Dev Footer  -->
                <draw:SkiaStack
                    Opacity="0.75"
                    Spacing="4"
                    HorizontalOptions="Fill"
                    Margin="6">

                    <draw:SkiaRichLabel
                        HorizontalOptions="Center"
                        FontSize="10.0"                 
                        TextColor ="#55FFFFFF"
                        Text="{x:Static resX:ResStrings.Settings_Copyright}" />

                    <draw:SkiaLabel
                        HorizontalOptions="Center"
                        FontSize="10.0"                 
                        TextColor ="#55FFFFFF"
                        Text="www.artoffoto.com" />

                    <draw:SkiaRichLabel
                        x:Name="LabelBuild"
                        HorizontalOptions="Center"
                        FontSize="10.0"                 
                        TextColor ="#55FFFFFF"/>

                </draw:SkiaStack>

            </draw:SkiaStack>

        </draw:SkiaScroll>

    </draw:Canvas>

</pages:IncludedContent>