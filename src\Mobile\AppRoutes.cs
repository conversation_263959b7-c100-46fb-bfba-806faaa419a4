﻿using System.Reflection;
using AppoMobi.UI;

namespace AppoMobi;

// <summary>

/// <summary>
/// For intellisense to help selecting routes.
/// </summary>
public static class AppRoutes
{
    public static readonly (string Route, Type Type) Drawn = ("drawn", typeof(AppShell));
    public static readonly (string Route, Type Type) Tabs = ("tabs", typeof(PageTabs));
    public static readonly (string Route, Type Type) Widgets = ("widgets", typeof(PageSwitcher));
    public static readonly (string Route, Type Type) Dev = ("dev", typeof(DevPage));

    //public static readonly (string Route, Type Type) Debug = ("about", typeof(PageAboutUs));
    //public static readonly (string Route, Type Type) Debug = ("news", typeof(PageWebNews));

    public static IEnumerable<(string, Type)> GetRoutes()
    {
        return typeof(AppRoutes)
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            //.Where(f => f.FieldType == typeof((string, Type)))
            .Select(f => ((string, Type))f.GetValue(null));
    }

    public static string RootDefault
    {
        get
        {
            //return "//" + Dev.Route;

            return "//" + Widgets.Route;
        }
    }
}

/// <summary>
/// For intellisense to help selecting routes.
/// </summary>
public static class AppDrawnRoutes
{
    public static MauiAppBuilder RegisterTransientShellRoutes(this MauiAppBuilder builder)
    {
        foreach (var appRoute in GetRoutes())
        {
            builder.Services.AddTransient(appRoute.Item2); //used in tabs
        }
        return builder;
    }

    public static readonly (string Route, Type Type) Test = ("test", typeof(TestScreen));
    public static readonly (string Route, Type Type) Widgets = ("widgets", typeof(ScreenWidgets));

    public static IEnumerable<(string, Type)> GetRoutes()
    {
        Type currentType = MethodBase.GetCurrentMethod().DeclaringType;
        return currentType
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            //.Where(f => f.FieldType == typeof((string, Type)))
            .Select(f => ((string, Type))f.GetValue(null));
    }

    public static string RootDefault
    {
        get
        {
            return "//" + Widgets.Route;
        }
    }
}

