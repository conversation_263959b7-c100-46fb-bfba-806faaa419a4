using System.Windows.Input;
using AppoMobi.Main;
using AppoMobi.Mobile.Import.Common.ResX;
using AppoMobi.Mobile.Views.Popups;
using AppoMobi.Specials;
using DrawnUi.Controls;
using DrawnUi.Views;
 
using static System.Net.Mime.MediaTypeNames;

namespace AppoMobi.Xam
{
    /// <summary>
    /// DrawnUI version of PromptEnterInteger.
    /// 
    /// Usage example:
    /// PromptEnterIntegerDrawn.Show(new Command((object context) => {
    ///     var value = (int)context;
    ///     // Handle the entered integer
    /// }), "Enter Value", 0);
    /// 
    /// </summary>
    public class PromptEnterIntegerDrawn : AppoMobi.Main.AppScreen
    {
        private SkiaRichLabel _titleLabel;
        private SkiaMauiEntry _entryControl;

        public ICommand CallbackCommand { get; set; }

        private int _data;
        public int Data
        {
            get { return _data; }
            set
            {
                if (_data != value)
                {
                    _data = value;
                    UpdateValueDisplay();
                }
            }
        }

        private string _title;
        public string Title
        {
            get { return _title; }
            set
            {
                if (_title != value)
                {
                    _title = value;
                    UpdateTitleDisplay();
                }
            }
        }

        public PromptEnterIntegerDrawn(ICommand callback, string title, int defaultValue)
        {
            CallbackCommand = callback;
            Title = title;
            Data = defaultValue;

            Tapped += (sender, args) =>
            {
                Dismiss();
            };

            VerticalOptions = LayoutOptions.Fill;
            HorizontalOptions = LayoutOptions.Fill;

            CreateContent();
        }

        private void CreateContent()
        {
            Children = new List<SkiaControl>()
            {
                new SkiaLayout()
                {
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaLayout()
                        {
                            WidthRequest = 250,
                            Type = LayoutType.Column,
                            Spacing = 0,
                            BackgroundColor = BackColors.OptionLine,
                            Children = new List<SkiaControl>()
                            {
                                // HEADER
                                new SkiaLayout
                                {
                                    UseCache = SkiaCacheType.Image,
                                    Type = LayoutType.Absolute,
                                    HorizontalOptions = LayoutOptions.Fill,
                                    VerticalOptions = LayoutOptions.Start,
                                    FillGradient = new SkiaGradient()
                                    {
                                        StartXRatio = 1,
                                        EndXRatio = 0,
                                        StartYRatio = 0,
                                        EndYRatio = 0,
                                        Colors = new Color[] { BackColors.GradientStartNav, BackColors.GradientEndNav }
                                    },
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaControl() { BackgroundColor = Color.Parse("#33000000") }.Fill(),
                                        new SkiaRichLabel()
                                        {
                                            VerticalOptions = LayoutOptions.Center,
                                            FontFamily = "ui",
                                            FontSize = 14,
                                            Text = "header",
                                            Margin = new Thickness(20, 8, 8, 8),
                                            TextColor = Colors.WhiteSmoke,
                                            VerticalTextAlignment = TextAlignment.Center
                                        }.Assign(out _titleLabel)
                                    }
                                },

                                // ENTRY FIELD
                                new SkiaShape
                                {
                                    BackgroundColor = Colors.White,
                                    CornerRadius = 4,
                                    Margin = new Thickness(16),
                                    Padding = new Thickness(1),
                                    HeightRequest = 44,
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaEntryInteger()
                                        {
                                            FontSize = 14,
                                            TextColor = Colors.Black,
                                            HorizontalOptions = LayoutOptions.Fill,
                                            VerticalOptions = LayoutOptions.Fill,
                                        }.Assign(out _entryControl)
                                        .ObserveSelf((entry, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(SkiaMauiEntry.Text)))
                                            {
                                                if (int.TryParse(entry.Text, out int value))
                                                {
                                                    Data = value;
                                                }
                                            }
                                        })
                                    }
                                },

                                // OK BUTTON
                                Elements.CreateButton(ResStrings.BtnOk)
                                    .FillX()
                                    .WithMargin(16, 0, 16, 16)
                                    .OnTapped(me =>
                                    {
                                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(150), () =>
                                        {
                                            CallbackCommand?.Execute(Data);
                                            Dismiss();
                                        });
                                    })
                            }
                        }
                         .OnTapped(me =>
                        {
                            //block taps not to close
                        })
                    }
                }
            };

            UpdateTitleDisplay();
            UpdateValueDisplay();
        }

        public void Dismiss()
        {
            PopupPage.ClosePopup();
        }

        private void UpdateTitleDisplay()
        {
            if (_titleLabel != null)
            {
                _titleLabel.Text = Title ?? "";
            }
        }

        private void UpdateValueDisplay()
        {
            if (_entryControl != null)
            {
                _entryControl.Text = Data.ToString();
            }
        }

        public static void Show(ICommand callback, string title, int defaultValue)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var popupContent = new PromptEnterIntegerDrawn(callback, title, defaultValue);

                var popup = new CustomPopup()
                {
                    Content = new ContentView() { Content = new ScreenCanvas() { Content = popupContent } },
                };

                popup.Open(true);
            });
        }

        public static async Task<int?> ShowAsync(string title, int defaultValue, ICommand callback = null)
        {
            var tcs = new TaskCompletionSource<int?>();

            var popupContent = new PromptEnterIntegerDrawn(
                new Command<int>(result =>
                {
                    tcs.SetResult(result);
                    callback?.Execute(result);
                }),
                title,
                defaultValue);

            var popup = new CustomPopup()
            {
                Content = new ContentView()
                {
                    Content = new ScreenCanvas()
                    {
                        Content = popupContent,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.Fill
                    }
                },
                HorizontalOptions = Microsoft.Maui.Primitives.LayoutAlignment.End,
                VerticalOptions = Microsoft.Maui.Primitives.LayoutAlignment.Center
            };

            popup.Open(true);

            var result = await tcs.Task;
            popup.Dismiss();

            return result;
        }
    }
}
