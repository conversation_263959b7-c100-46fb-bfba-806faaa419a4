using AppoMobi.Main;
using AppoMobi.Mobile.Import.Common.ResX;
using AppoMobi.Mobile.Views.Popups;
using AppoMobi.Forms.Models;
using AppoMobi.Specials;
using AppoMobi.Xam;
using DrawnUi.Views;
using System.Windows.Input;
using System.Collections.ObjectModel;

namespace AppoMobi.Xam
{
    /// <summary>
    /// DrawnUI version of EditableListView.
    /// 
    /// Usage example:
    /// EditableListViewDrawn.Show(new Command((object context) => {
    ///     var items = (ObservableCollection<OptionItem>)context;
    ///     // Handle the edited list
    /// }), "Edit Items", existingItems);
    /// 
    /// </summary>
    public class EditableListViewDrawn : AppoMobi.Main.AppScreen
    {
        public static bool IntantSave = false;

        public EditableListViewDrawn(ICommand callback, string title, IList<OptionItem> items)
        {
            CallbackCommand = callback;
            Title = title;
            MenuList = new ObservableCollection<OptionItem>(items ?? new List<OptionItem>());

            Tapped += (sender, args) => { Dismiss(); };

            VerticalOptions = LayoutOptions.Fill;
            HorizontalOptions = LayoutOptions.Fill;

            CreateContent();
        }


        private SkiaRichLabel _titleLabel;
        private SkiaScroll _scrollContainer;
        private SkiaLayout _itemsContainer;

        public ICommand CallbackCommand { get; set; }
        public ObservableCollection<OptionItem> MenuList { get; private set; }

        private string _title;
        public string Title
        {
            get { return _title; }
            set
            {
                if (_title != value)
                {
                    _title = value;
                    UpdateTitleDisplay();
                }
            }
        }


        private void CreateContent()
        {
            Children = new List<SkiaControl>()
            {
                new SkiaLayout()
                {
                    HorizontalOptions = LayoutOptions.End,
                    VerticalOptions = LayoutOptions.Center,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaLayout()
                        {
                            Type = LayoutType.Column,
                            Spacing = 0,
                            BackgroundColor = BackColors.OptionLine,
                            //HorizontalOptions = LayoutOptions.Fill,
                            WidthRequest = 250,
                            //VerticalOptions = LayoutOptions.Center,
                            Children = new List<SkiaControl>()
                            {
                                // HEADER
                                new SkiaLayout
                                {
                                    UseCache = SkiaCacheType.Image,
                                    Type = LayoutType.Absolute,
                                    HorizontalOptions = LayoutOptions.Fill,
                                    VerticalOptions = LayoutOptions.Start,
                                    HeightRequest = 50,
                                    FillGradient = new SkiaGradient()
                                    {
                                        StartXRatio = 1,
                                        EndXRatio = 0,
                                        StartYRatio = 0,
                                        EndYRatio = 0,
                                        Colors = new Color[] { BackColors.GradientStartNav, BackColors.GradientEndNav }
                                    },
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaControl() { BackgroundColor = Color.Parse("#33000000") }.Fill(),
                                        new SkiaRichLabel()
                                        {
                                            VerticalOptions = LayoutOptions.Center,
                                            FontFamily = "ui",
                                            FontSize = 14,
                                            Text = "header",
                                            Margin = new Thickness(20, 8, 8, 8),
                                            TextColor = Colors.WhiteSmoke,
                                            VerticalTextAlignment = TextAlignment.Center
                                        }.Assign(out _titleLabel)
                                    }
                                },

                                // SCROLLABLE CONTENT
                                new SkiaScroll()
                                {
                                    Bounces = false,
                                    Orientation = ScrollOrientation.Vertical,
                                    HorizontalOptions = LayoutOptions.Fill,
                                    //VerticalOptions = LayoutOptions.Fill
                                }
                                .Assign(out _scrollContainer)
                                .WithContent(
                                    new SkiaLayout()
                                    {
                                        Type = LayoutType.Column,
                                        Spacing = 0.5,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        VerticalOptions = LayoutOptions.Start
                                    }.Assign(out _itemsContainer)
                                ),

                                // FOOTER BUTTONS
                                new SkiaLayout()
                                {
                                    Type = LayoutType.Column,
                                    Spacing = 8,
                                    Margin = new Thickness(16),
                                    HorizontalOptions = LayoutOptions.Fill,
                                    VerticalOptions = LayoutOptions.End,
                                    Children = new List<SkiaControl>()
                                    {
                                        Elements.CreateButton("Add")
                                            .FillX()
                                            .OnTapped(me => OnAddTapped()),

                                        Elements.CreateButton(ResStrings.BtnOk)
                                            .FillX()
                                            .WithVisibility(!IntantSave)
                                            .OnTapped(me =>
                                            {
                                                Tasks.StartDelayed(TimeSpan.FromMilliseconds(150), () =>
                                                {
                                                    CallbackCommand?.Execute(MenuList);
                                                    Dismiss();
                                                });
                                            })
                                    }
                                }
                            }
                        }
                    }
                }
                .OnTapped(me =>
                {
                    //block taps not to close
                })
            };

            UpdateTitleDisplay();
            RefreshItemsList();
        }

        private SkiaControl CreateListItem(OptionItem item)
        {
            return new SkiaLayout()
            {
                UseCache = SkiaCacheType.Image,
                Type = LayoutType.Column,
                Spacing = 0,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Start,
                Children = new List<SkiaControl>()
                {
                    new SkiaLayout()
                    {
                        Type = LayoutType.Grid,
                        HorizontalOptions = LayoutOptions.Fill,
                        HeightRequest = 50,
                        ColumnSpacing = 0,
                        ColumnDefinitions = new ColumnDefinitionCollection()
                        {
                            new ColumnDefinition(new GridLength(12, GridUnitType.Absolute)),
                            new ColumnDefinition(GridLength.Star),
                            new ColumnDefinition(new GridLength(50, GridUnitType.Absolute))
                        },
                        Children = new List<SkiaControl>()
                        {
                            new SkiaLabel()
                            {
                                Text = item.Title,
                                FontSize = 15,
                                TextColor = TextColors.GreyDark,
                                Margin = new Thickness(8, 15, 4, 15),
                                HorizontalOptions = LayoutOptions.Fill,
                                VerticalTextAlignment = TextAlignment.Center,
                                VerticalOptions = LayoutOptions.Fill
                            }
                            .OnTapped(me => OnEditItem(item))
                            .WithColumn(1),

                            new FontIconLabelDrawn()
                            {
                                Text = FaPro.CircleXmark,
                                FontSize = 13,
                                TextColor = Color.Parse("#ccaa8888"),
                                HorizontalOptions = LayoutOptions.Center,
                                VerticalOptions = LayoutOptions.Center,
                                Margin = new Thickness(0, 0, 12, 0)
                            }.WithColumn(2)
                            .OnTapped(me => OnDeleteItem(item)),
                        }
                    },

                    // Divider
                    new SkiaShape()
                    {
                        Type = ShapeType.Rectangle,
                        BackgroundColor = Colors.LightGray,
                        HeightRequest = 0.5,
                        HorizontalOptions = LayoutOptions.Fill
                    }
                }
            };
        }

        private void RefreshItemsList()
        {
            if (_itemsContainer == null) return;

            _itemsContainer.Children.Clear();
            foreach (var item in MenuList)
            {
                _itemsContainer.Children.Add(CreateListItem(item));
            }
        }

        private void OnEditItem(OptionItem item)
        {
            // For now, just a placeholder - could open edit dialog
            System.Diagnostics.Debug.WriteLine($"[EDIT ITEM] {item.Title}");
        }

        private void OnDeleteItem(OptionItem item)
        {
            if (MenuList.Count > 1)
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    MenuList.Remove(item);
                    RefreshItemsList();

                    //insta-save:
                    if (IntantSave)
                        CallbackCommand?.Execute(MenuList);
                });
            }
        }

        private void OnAddTapped()
        {
            PromptEnterIntegerDrawn.Show(new Command((object context) =>
            {
                var id = (int)context;

                if (id == 0)
                {
                    return;
                }

                var existing = MenuList.FirstOrDefault(x => x.Id == id.ToString());

                if (existing == null)
                {
                    existing = new OptionItem
                    {
                        Id = id.ToString(),
                        Title = $"{id} mm"
                    };

                    //todo define position

                    var ids = MenuList.Select(x => x.Id.ToInteger()).ToList();
                    ids.Add(existing.Id.ToInteger());
                    var ordered = ids.OrderBy(x => x).ToList();

                    var index = 0;
                    foreach (var item in MenuList)
                    {
                        if (index > ordered.Count)
                        {
                            break;
                        }

                        if (item.Id.ToInteger() != ordered[index])
                        {
                            break;
                        }

                        index++;
                    }

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        MenuList.Insert(index, existing);
                        RefreshItemsList();

                        //insta-save:
                        if (IntantSave)
                            CallbackCommand?.Execute(MenuList);
                    });




                }

            }), ResStrings.AddLens, 0);
        }

        public void Dismiss()
        {
            PopupPage.ClosePopup();
        }

        private void UpdateTitleDisplay()
        {
            if (_titleLabel != null)
            {
                _titleLabel.Text = Title ?? "";
            }
        }

        public static void Show(ICommand callback, string title, IList<OptionItem> items)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var popupContent = new EditableListViewDrawn(callback, title, items);

                var popup = new CustomPopup()
                {
                    Content = new ContentView()
                    {
                        Content = new ScreenCanvas()
                        {
                            Content = popupContent
                        }
                    },
                };

                popup.Open(true);
            });
        }
    }
}
