﻿<?xml version="1.0" encoding="utf-8"?>

<views:BasePageCodeBehind
    x:Class="Sandbox.Views.MainPageGif"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Sandbox.Views.Controls"
    xmlns:demo="clr-namespace:Sandbox"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:gestures="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:system="clr-namespace:System;assembly=System.Runtime"
    xmlns:views="clr-namespace:Sandbox.Views"
    x:DataType="views:MainPageGif"
    BackgroundColor="Black">

    <draw:Canvas
        Margin="0,32,0,0"
        BackgroundColor="Black"
        Gestures="Enabled"
        RenderingMode="Accelerated"
        HorizontalOptions="Fill"
        Tag="MainPage"
        VerticalOptions="Fill">

        <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">

            <draw:SkiaScroll HorizontalOptions="Fill" VerticalOptions="Fill">

                <draw:SkiaLayout
                    Padding="20,20,20,20"
                    BackgroundColor="DarkSlateGrey"
                    HorizontalOptions="Fill"
                    Spacing="20"
                    Tag="SkiaStack"
                    Type="Column"
                    UseCache="None">

                    <draw:SkiaRichLabel
                        Margin="16,0"
                        FontFamily="FontText"
                        FontSize="15"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="`SkiaMediaImage` (at the top) is a subclassed `SkiaImage` for displaying any kind of images, while `SkiaGif` (the second control) is a dedicated lightweight GIF-player."
                        TextColor="White"
                        UseCache="Operations" />

                    <!--<draw:SkiaImage Source="Images/8.jpg" HeightRequest="100" WidthRequest="100" 
                                    Aspect="AspectFitFill"
                                    RescalingQuality="High" 
                                    UseCache="Image"
                                    HorizontalOptions="Center" IsVisible="{Binding Visibility}" />

                    <draw:SkiaImage Source="Images/maui1.png" HeightRequest="100" WidthRequest="100"
                                    Aspect="AspectFitFill" 
                                    RescalingQuality="High" 
                                    UseCache="Image"
                                    HorizontalOptions="Center" />-->

                    <draw:SkiaButton
                        ControlStyle="Cupertino"
                        HeightRequest="38"
                        HorizontalOptions="Center"
                        Tapped="SkiaButton_OnTapped"
                        Text="Toggle Visibility"
                        WidthRequest="200" />

                    <draw:SkiaMediaImage
                        IsVisible="{Binding Visibility}" 
                        HorizontalOptions="Fill"
                        Source="Anims/look.webp" />

                    <draw:SkiaGif
                        BackgroundColor="Gainsboro"
                        HorizontalOptions="Fill"
                        Repeat="-1"
                        Source="Anims/hair.webp" />

                </draw:SkiaLayout>

            </draw:SkiaScroll>

            <controls:ButtonToRoot />

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>

</views:BasePageCodeBehind>