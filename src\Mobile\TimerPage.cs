﻿using AppoMobi.Main;
using DrawnUi.Views;

namespace AppoMobi.Mobile
{
    public class TimerPage : BasePageReloadable
    {
        public override void Build()
        {
            Canvas?.Dispose();

            Canvas = new AppCanvas(new StepsTimer());

            this.Content = Canvas;
        }

        Canvas Canvas;

        protected override void OnDisappearing()
        {
            base.OnDisappearing();

#if IOS

            Super.ShowStatusBar();

#endif
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            #if IOS

            Super.HideStatusBar();

            #endif
        }

        protected override void Dispose(bool isDisposing)
        {
            if (isDisposing)
            {
                this.Content = null;
                Canvas?.Dispose();
            }

            base.Dispose(isDisposing);
        }
    }
}
