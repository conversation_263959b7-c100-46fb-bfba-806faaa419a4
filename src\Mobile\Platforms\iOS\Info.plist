<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleVersion</key>
	<string>900385</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>XSAppIconAssets</key>
	<string>Assets.xcassets/original.appiconset</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>NSCalendarsUsageDescription</key>
	<string>May access calendar</string>
	<key>NSCameraUsageDescription</key>
	<string>Allow access to the camcorder for the camera</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Allow microphone access for the camera</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow access to the library to save photos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow access to the library to save photos</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To be able to geotag photos</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>instagram</string>
		<string>fb</string>
		<string>vk</string>
		<string>yandexmaps</string>
		<string>yandexnavi</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>CFBundleLocalizations</key>
	<array>
		<string>ru</string>
		<string>fr</string>
		<string>de</string>
		<string>es</string>
		<string>zh</string>
		<string>ko</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
</dict>
</plist>
