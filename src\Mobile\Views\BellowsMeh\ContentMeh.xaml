﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:IncludedContent
    x:Class="AppoMobi.Main.ContentMeh"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:SkiaSharp.Views.Maui.Controls;assembly=SkiaSharp.Views.Maui.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="MainScroll"
    Padding="20,0,20,0">

    <VerticalStackLayout Spacing="16" VerticalOptions="Start">
        <!--  Focal Length фокусное расстояние объектива  -->
        <!--  переключатель между дюймами и мм  -->


        <!--  Bellows Extension длина меха в мм  -->
        <!--  переключатель между дюймами и мм  -->

        <!--  entries  -->
        <StackLayout
            x:Name="cRegister"
            Spacing="10"
            VerticalOptions="Start">

            <StackLayout
                x:Name="cFstopStack"
                Margin="0,20,0,10"
                Spacing="4">

                <Label
                    FontSize="24"
                    HorizontalOptions="Center"
                    Text="{x:Static resX:ResStrings.X_BellowsResult}"
                    TextColor="{x:Static xam:TextColors.EntryDesc}" />

                <Label
                    x:Name="cOutput"
                    Padding="4"
                    FontSize="55"
                    HorizontalOptions="Center"
                    TextColor="{x:Static xam:TextColors.Result}" />

            </StackLayout>


            <!--  entry  -->
            <Grid HorizontalOptions="Center" WidthRequest="250">

                <xam:UnderlinedEntry
                    x:Name="EntryFocal"
                    FadeUnfocused="True"
                    HorizontalOptions="Fill"
                    WidthRequest="-1" />

                <!--<xam:FontIconLabel x:Name="cIcon1" HorizontalOptions="Start" VerticalOptions="Center"  Margin="0,0,0,14"/>-->


                <Label
                    x:Name="cDesc1"
                    Margin="0,10,0,0"
                    HorizontalOptions="End"
                    Text="''"
                    VerticalOptions="Center" />

                <!--  hotspot  -->
                <gestures:LegacyGesturesBoxView
                    Margin="0,0,0,4"
                    Down="UnitsFocal_OnTapped"
                    HeightRequest="40"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    WidthRequest="60" />
            </Grid>

            <!--  entry  -->
            <Grid HorizontalOptions="Center" WidthRequest="250">

                <xam:UnderlinedEntry
                    x:Name="EntryBellows"
                    FadeUnfocused="True"
                    HorizontalOptions="Fill"
                    WidthRequest="-1" />

                <!--<xam:FontIconLabel x:Name="cIcon2" HorizontalOptions="Start" VerticalOptions="Center"  Margin="0,0,0,14"/>-->


                <Label
                    x:Name="cDesc2"
                    Margin="0,10,0,0"
                    HorizontalOptions="End"
                    Text="mm"
                    VerticalOptions="Center" />

                <!--  hotspot  -->
                <gestures:LegacyGesturesBoxView
                    Margin="0,0,0,4"
                    Down="UnitsExtension_OnTapped"
                    HeightRequest="40"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    WidthRequest="60" />
            </Grid>


        </StackLayout>

        <draw:Canvas HorizontalOptions="Center"
                                     Margin="0,24,0,0">
            <draw:SkiaRichLabel
                UseCache="Operations"
                x:Name="cHelp"
                FontSize="12"
                HorizontalOptions="Fill"
                HorizontalTextAlignment="Center"
                LineBreakMode="WordWrap"
                Text="This is a help text."
                TextColor="{x:Static xam:TextColors.EntryDesc}"
                WidthRequest="200" />
        </draw:Canvas>


    </VerticalStackLayout>

</pages:IncludedContent>