﻿namespace AppoMobi.Mobile.Views
{
    public class TestScreen : AppScreen
    {
        public TestScreen()
        {
            Type = LayoutType.Column;
            Children = new List<SkiaControl>()
            {
                new SkiaRichLabel("Test")
                {
                    FontSize = 16,
                    FontFamily = "FontTextTitle",
                    TextColor = AppColors.Icons,
                    UseCache = SkiaCacheType.Operations
                }.CenterX(),
                new SkiaImage(@"Images\close3b.png")
                {
                    HeightRequest = AppUI.NavbarIconSize,
                    //RescalingQuality = SKFilterQuality.High,
                    //UseCache = SkiaCacheType.Image,
                    Aspect = TransformAspect.AspectFit,
                }.CenterX(),
            };
        }
    }
}
