﻿using System.Diagnostics;
using AppoMobi.Helpers;
using AppoMobi.Maui.DrawnUi.Demo.Views.Controls;
using AppoMobi.Models;
using AppoMobi.Xam;
using DrawnUi.Controls;
using Microsoft.Maui.Controls;
using static System.Net.Mime.MediaTypeNames;

namespace AppoMobi.Main
{
    public class StepsTimer : AppScreen
    {
        //Why we use SkiaRichLabel instead of SkiaLabel?
        //To make sure if our font doesn't have symbols for some language we use
        //this control will auto-find installed font that can display this

        /// <summary>
        /// Running now with current values, created from AvailableTimers
        /// </summary>
        public List<RunningTimer> Timers { get; } = new();

        /// <summary>
        /// All saved steps, can be re-created from EditingTimers if settings are saved
        /// </summary>
        public List<TimerStep> AvailableTimers
        {
            get => availableTimers;
            set
            {
                if (Equals(value, availableTimers))
                {
                    return;
                }

                availableTimers = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Temporary snapshot to edit without applying yet, created from AvailableTimers
        /// </summary>
        public List<TimerStep> EditingTimers
        {
            get => editingTimers;
            set
            {
                if (Equals(value, editingTimers))
                {
                    return;
                }

                editingTimers = value;
                OnPropertyChanged();
            }
        }

        private RunningTimer _currentTimer = new();

        public RunningTimer CurrentTimer
        {
            get => _currentTimer;
            set
            {
                if (_currentTimer != value)
                {
                    var oldTimer = _currentTimer;
                    _currentTimer = value;
                    OnPropertyChanged();

                    // When CurrentTimer changes, update the corresponding timer in Timers collection
                    OnCurrentTimerChanged(oldTimer, _currentTimer);
                }
            }
        }

        /// <summary>
        /// Called when CurrentTimer changes to synchronize time changes with the Timers collection
        /// </summary>
        private void OnCurrentTimerChanged(RunningTimer oldTimer, RunningTimer newTimer)
        {
            if (oldTimer != null && newTimer != null && oldTimer.Id != newTimer.Id)
            {
                // Find the corresponding timer in Timers collection and update its time
                var timerInCollection = Timers.FirstOrDefault(t => t.Id == newTimer.Id);
                if (timerInCollection != null && timerInCollection != newTimer)
                {
                    // Sync the time from the new current timer to the timer in collection
                    timerInCollection.Time = newTimer.Time;
                    timerInCollection.Progress = newTimer.Progress;
                }
            }
        }

        /// <summary>
        /// Update the corresponding timer in Timers collection when CurrentTimer time changes
        /// </summary>
        private void UpdateTimerInCollection(RunningTimer currentTimer)
        {
            if (currentTimer != null)
            {
                var timerInCollection = Timers.FirstOrDefault(t => t.Id == currentTimer.Id);
                if (timerInCollection != null)
                {
                    timerInCollection.Time = currentTimer.Time;
                    timerInCollection.Progress = currentTimer.Progress;
                }
            }
        }

        private int visibleScreen = 0;

        /// <summary>
        /// Controls which sub-screen will be visible, by index
        /// </summary>
        public int VisibleScreen
        {
            get => visibleScreen;
            set
            {
                if (value == visibleScreen)
                {
                    return;
                }

                visibleScreen = value;
                OnPropertyChanged();
                Update();
            }
        }

        public override void OnWillDisposeWithChildren()
        {
            base.OnWillDisposeWithChildren();

            SetupSystemScreen(false);
            StopTimer(false);
            RadioButtons.All.Changed -= OnRadioChanged;
        }

        async Task InitSoundAsync()
        {
            await AudioPlayer.PreloadAsync("30", @"Sounds\30s.wav");
            await AudioPlayer.PreloadAsync("60", @"Sounds\60s.wav");
        }

        public void SetupSystemScreen(bool start)
        {
            //todo change phont system status bar text upon skin

            if (start)
            {
                Core.Native.ExecuteTask("cannotSleep");
            }
            else
            {
                if (Settings.Current.OptionScreenOn != "yes")
                {
                    Core.Native.ExecuteTask("canSleep");
                }
            }
        }

        public StepsTimer()
        {
            BackgroundColor = Colors.Black;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            SetupSystemScreen(true);

            _ = InitSoundAsync();

            // Load saved timers or use preset
            List<TimerStep> savedTimers = TimerStep.Load();
            if (savedTimers != null && savedTimers.Count > 0)
            {
                // Ensure all loaded timers have IDs
                foreach (var timer in savedTimers)
                {
                    if (timer.Id == Guid.Empty)
                        timer.Id = Guid.NewGuid();
                }

                AvailableTimers.AddRange(savedTimers);
            }
            else
            {
                // Use preset if no saved timers
                AvailableTimers.AddRange(new List<TimerStep>()
                {
                    new() { Title = ResStrings.TimerStepSoak, Time = new TimeSpan(0, 0, 40), },
                    new() { Title = ResStrings.TimerStepDeveloper, Time = new TimeSpan(0, 0, 10), },
                    new() { Title = ResStrings.TimerStopBath, Time = new TimeSpan(0, 0, 10), },
                    new() { Title = ResStrings.TimerStepFixer, Time = new TimeSpan(0, 0, 10), },
                    new() { Title = ResStrings.TimerStepWash, Time = new TimeSpan(0, 0, 10), },

                    ////new() { Title = ResStrings.TimerStepSoak, Time = new TimeSpan(0, 3, 0), },
                    ////new() { Title = ResStrings.TimerStepDeveloper, Time = new TimeSpan(0, 12, 0), },
                    ////new() { Title = ResStrings.TimerStopBath, Time = new TimeSpan(0, 1, 0), },
                    ////new() { Title = ResStrings.TimerStepFixer, Time = new TimeSpan(0, 5, 0), },
                    ////new() { Title = ResStrings.TimerStepWash, Time = new TimeSpan(0, 20, 0), },
                });
            }

            PopulateTimers();

            if (Timers.Count > 0)
            {
                SetTimerAsCurrent(Timers[0]);
            }

            InitPickers();

            RadioButtons.All.Changed += OnRadioChanged;

            var skin = Preferences.Get("TimerSkin", 0);
            SelectSkin(skin);
        }

        #region TIMER

        private System.Timers.Timer _countdownTimer;
        private DateTime _startTime;
        private TimeSpan _remainingTime;
        private bool _isRunning;
        private bool _isPaused;

        public bool IsRunning
        {
            get => _isRunning;
            set
            {
                if (_isRunning != value)
                {
                    _isRunning = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsPaused
        {
            get => _isPaused;
            set
            {
                if (_isPaused != value)
                {
                    _isPaused = value;
                    OnPropertyChanged();
                }
            }
        }

        void SetTimerAsCurrent(RunningTimer timer)
        {
            CurrentTimer = timer;

            // Reset the next timer's time and progress
            CurrentTimer.Time = CurrentTimer.Timer.Time;
            CurrentTimer.Progress = 0;
        }

        void StartTimer()
        {
            if (CurrentTimer?.Timer == null) return;

            AudioPlayer.PlayPreloaded("60");

            if (IsPaused)
            {
                // Resume from pause
                _startTime = DateTime.Now - (CurrentTimer.Timer.Time - _remainingTime);
                IsPaused = false;
                Debug.WriteLine($"Timer '{CurrentTimer.Timer.Title}' Resumed!");
            }
            else
            {
                // Fresh start
                _startTime = DateTime.Now;
                _remainingTime = CurrentTimer.Timer.Time;
                CurrentTimer.Time = CurrentTimer.Timer.Time;
                CurrentTimer.Progress = 0;

                Debug.WriteLine($"Timer '{CurrentTimer.Timer.Title}' Started!");

                _lastSecondPlayed = (int)Math.Ceiling(CurrentTimer.Timer.Time.TotalSeconds);
            }

            IsRunning = true;

            _countdownTimer?.Stop();
            _countdownTimer = new System.Timers.Timer(100); // Update every 100ms for smooth progress
            _countdownTimer.Elapsed -= OnTimerTick;
            _countdownTimer.Elapsed += OnTimerTick;
            _countdownTimer.Start();
        }

        void StopTimer(bool reset)
        {
            _countdownTimer?.Stop();
            _countdownTimer?.Dispose();
            _countdownTimer = null;

            IsRunning = false;
            IsPaused = false;

            // Reset to original time
            if (reset && CurrentTimer?.Timer != null)
            {
                CurrentTimer.Time = CurrentTimer.Timer.Time;
                CurrentTimer.Progress = 0;

                // Update the corresponding timer in Timers collection
                UpdateTimerInCollection(CurrentTimer);
            }
        }

        void PauseTimer()
        {
            if (!IsRunning) return;

            _countdownTimer?.Stop();
            IsPaused = true;
            IsRunning = false;
        }

        private int _lastSecondPlayed = -1;

        /// <summary>
        /// Timer tick handler that updates the display
        /// </summary>
        private void OnTimerTick(object sender, System.Timers.ElapsedEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (CurrentTimer?.Timer == null)
                {
                    return;
                }

                var elapsed = DateTime.Now - _startTime;
                _remainingTime = CurrentTimer.Timer.Time - elapsed;

                if (_remainingTime <= TimeSpan.Zero)
                {
                    _remainingTime = TimeSpan.Zero;
                    CurrentTimer.Time = TimeSpan.Zero;
                    CurrentTimer.Progress = 100;

                    // Update the corresponding timer in Timers collection
                    UpdateTimerInCollection(CurrentTimer);

                    StopTimer(false);
                    OnTimerCompleted();
                }
                else
                {
                    // Round remaining time to nearest second for display
                    var displayTime = TimeSpan.FromSeconds(Math.Ceiling(_remainingTime.TotalSeconds));

                    // Don't let display time exceed original timer duration
                    if (displayTime > CurrentTimer.Timer.Time)
                    {
                        displayTime = CurrentTimer.Timer.Time;
                    }

                    CurrentTimer.Time = displayTime;

                    var totalSeconds = CurrentTimer.Timer.Time.TotalSeconds;
                    var remainingSeconds = _remainingTime.TotalSeconds;
                    CurrentTimer.Progress = ((totalSeconds - remainingSeconds) / totalSeconds) * 100;


                    // Update the corresponding timer in Timers collection
                    UpdateTimerInCollection(CurrentTimer);

                    // Play sound based on conditions:
                    // A: Every 30 seconds remaining (30, 60, 90, 120, etc.)
                    // B: Every second when remaining < 10 seconds
                    int trackSeconds = (int)Math.Ceiling(_remainingTime.TotalSeconds);

                    bool shouldPlaySound = false;

                    if (trackSeconds < 10)
                    {
                        // Condition B: Play every second when less than 10 seconds remaining (9,8,7,6,5,4,3,2,1)
                        if (trackSeconds != _lastSecondPlayed && trackSeconds < _lastSecondPlayed)
                        {
                            shouldPlaySound = true;
                            _lastSecondPlayed = trackSeconds;
                        }
                    }
                    else
                    {
                        // Condition A: Play every 30 seconds remaining (30, 60, 90, 120, etc.)
                        if (trackSeconds % 30 == 0 && trackSeconds != _lastSecondPlayed)
                        {
                            shouldPlaySound = true;
                            _lastSecondPlayed = trackSeconds;
                        }
                    }

                    if (shouldPlaySound)
                    {
                        AudioPlayer.PlayPreloaded("30");
                    }
                }
            });
        }

        /// <summary>
        /// Called when current timer completes - handles sequential timer execution
        /// </summary>
        private void OnTimerCompleted()
        {
            var completedTimer = CurrentTimer;

            // Call the completion callback for this specific timer
            OnFinished(completedTimer);

            Debug.WriteLine($"Timer '{completedTimer.Timer.Title}' completed!");

            AudioPlayer.PlayPreloaded("60");

            // Auto-advance to next timer if available
            var currentIndex = Timers.IndexOf(CurrentTimer);
            if (currentIndex >= 0 && currentIndex < Timers.Count - 1)
            {
                // Ensure timer is properly stopped first
                _countdownTimer?.Stop();
                _countdownTimer?.Dispose();
                _countdownTimer = null;

                // Reset state flags
                IsRunning = false;
                IsPaused = false;

                // Move to next timer
                SetTimerAsCurrent(Timers[currentIndex + 1]);

                // Update the timer in collection
                UpdateTimerInCollection(CurrentTimer);

                //MainThread.BeginInvokeOnMainThread(async () => { StartTimer(); }); не стартует.
            }
            else
            {
                // All timers completed
                OnAllTimersFinished();
            }
        }

        void SelectNextTimer()
        {
            var currentIndex = Timers.IndexOf(CurrentTimer);
            if (currentIndex >= 0 && currentIndex < Timers.Count - 1)
            {
                SetTimerInternal(currentIndex + 1);
            }
        }

        void SelectPreviousTimer()
        {
            var currentIndex = Timers.IndexOf(CurrentTimer);
            if (currentIndex > 0)
            {
                SetTimerInternal(currentIndex - 1);
            }
        }

        void SelectFirstTimer()
        {
            var currentIndex = Timers.IndexOf(CurrentTimer);
            if (currentIndex > 0)
            {
                SetTimerInternal(0);
            }
        }

        void SetTimerInternal(int index)
        {
            // Ensure timer is properly stopped first
            _countdownTimer?.Stop();
            _countdownTimer?.Dispose();
            _countdownTimer = null;

            // Reset state flags
            IsRunning = false;
            IsPaused = false;

            // Move to next timer
            SetTimerAsCurrent(Timers[index]);

            // Update the timer in collection
            UpdateTimerInCollection(CurrentTimer);
        }

        /// <summary>
        /// Called when an individual timer finishes
        /// </summary>
        private void OnFinished(RunningTimer timer)
        {
            // Add your custom logic here - sound notification, vibration, etc.
            Debug.WriteLine($"Individual timer finished: {timer.Timer.Title}");

            // Play final completion sound etc
        }

        /// <summary>
        /// Called when all timers in the sequence have finished
        /// </summary>
        private void OnAllTimersFinished()
        {
            Debug.WriteLine("All timers completed!");

            // Play final completion sound etc


            // Optional: Reset all timers to their original state
            ResetAllTimers();
        }

        /// <summary>
        /// Reset all timers to their original time and progress
        /// </summary>
        private void ResetAllTimers()
        {
            foreach (var timer in Timers)
            {
                timer.Time = timer.Timer.Time;
                timer.Progress = 0;
            }

            // Reset to first timer
            if (Timers.Count > 0)
            {
                CurrentTimer = Timers[0];
            }
        }

        #endregion

        #region UI

        public Color TextColor = Colors.Red;

        private double StrokeWidth = 1.0;

        void SelectSkin(int skin)
        {
            if (skin == 0)
            {
                //red
                TextColor = Colors.Red;
            }
            else
            {
                TextColor = Colors.White;
            }

            Preferences.Set("TimerSkin", skin);
            Build();
        }

        void ExitScreen()
        {
            App.Instance.Messager.All("PopAll", "");
        }

        public void Build()
        {
            var pickerGradient = new SkiaGradient()
            {
                StartXRatio = 0,
                EndXRatio = 0,
                StartYRatio = 0,
                EndYRatio = 1,
                Colors = new Color[]
                    {
                        TextColor.WithAlpha(0.05f), TextColor.WithAlpha(0.1f), TextColor.WithAlpha(0.05f),
                    },
                ColorPositions = new List<double>() { 0, 0.5, 1 }
            };

            Children = new List<SkiaControl>()
            {
                new SkiaStack() //for top/bottom system insets
                {
                    HorizontalOptions = LayoutOptions.Fill,
                    Spacing = 0,
                    Children =
                    {
                        new SkiaLayer() //statusbar
                        {
                            HeightRequest = Super.Screen.TopInset
                        },

                        //SUBSCREENS
                        new SkiaLayer()
                        {
                            // TIMER SCREEN
                            new SkiaLayer()
                            {
                                Tag = "TIMER-0",
                                VerticalOptions = LayoutOptions.Fill,
                                Type = LayoutType.Column,
                                Padding = new(16, 24, 16, 24),
                                HorizontalOptions = LayoutOptions.Fill,
                                Children = new List<SkiaControl>()
                                {
                                    //container with title and close button
                                    new SkiaLayer()
                                    {
                                        /*

                                        //CLOSE
                                        new FontIconLabelDrawn()
                                        {
                                            Text = FaPro.ArrowLeftLong,
                                            FontSize = 20,
                                            TextColor = TextColor.WithAlpha(0.5f),
                                            HorizontalOptions = LayoutOptions.Start,
                                            VerticalOptions = LayoutOptions.Center,
                                            Margin = new Thickness(0, 8, 8, 0)
                                        },
                                        new SkiaHotspot()
                                        {
                                            HorizontalOptions = LayoutOptions.Start, WidthRequest = 44,
                                        }.OnTapped(me =>
                                        {
                                            ExitScreen();
                                        }),

                                        */

                                        //DISPLAY TIMER NAME
                                        new SkiaLabel()
                                        {
                                            Margin = new(0, 24, 0, 24),
                                            Text = ResStrings.Timer,
                                            HorizontalOptions = LayoutOptions.Fill,
                                            HorizontalTextAlignment = DrawTextAlignment.Center,
                                            FontSize = 24,
                                            MaxLines = 1,
                                            TextColor = TextColor,
                                            FontFamily = AppFonts.Title
                                        }.ObserveOn(
                                            this,
                                            () => CurrentTimer,
                                            nameof(CurrentTimer),
                                            (me, prop) =>
                                            {
                                                if (prop.IsEither(nameof(BindingContext), nameof(RunningTimer.Timer)))
                                                {
                                                    me.Text = $"{CurrentTimer.Timer.Title}";
                                                }
                                            }
                                        ),
                                    },


                                    //DISPLAY TIME
                                    new SkiaLabel()
                                    {
                                        HorizontalOptions = LayoutOptions.Center,
                                        MonoForDigits = "8",
                                        FontSize = 64,
                                        TextColor = TextColor,
                                        FontFamily = AppFonts.Title
                                    }.ObserveOn(
                                        this,
                                        () => CurrentTimer,
                                        nameof(CurrentTimer),
                                        (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(RunningTimer.Time)))
                                            {
                                                me.Text = $"{CurrentTimer.Time:mm\\:ss}";
                                            }
                                        }
                                    ),

                                    //DISPLAY TIMER PROGRESS
                                    new SkiaProgress()
                                        {
                                            Margin = 32,
                                            HeightRequest = 24,
                                            HorizontalOptions = LayoutOptions.Fill,
                                            ProgressColor = TextColor,
                                            TrackColor = TextColor.WithAlpha(0.33f),
                                            BackgroundColor = Colors.Transparent,
                                        }
                                        .ObserveOn(
                                            this,
                                            () => CurrentTimer,
                                            nameof(CurrentTimer),
                                            (me, prop) =>
                                            {
                                                if (prop.IsEither(nameof(BindingContext),
                                                        nameof(CurrentTimer.Progress)))
                                                {
                                                    me.Value = CurrentTimer.Progress;
                                                }
                                            }
                                        ),

                                    //PLAYER ICONS
                                    new SkiaRow()
                                    {
                                        UseCache = SkiaCacheType.Operations,
                                        HorizontalOptions = LayoutOptions.Center,
                                        Spacing = 20,
                                        Children =
                                        {
                                            //PREV
                                            new FontIconLabelDrawn()
                                                {
                                                    Text = FaPro.CircleLeft,
                                                    FontSize = 44,
                                                    TextColor = TextColor.WithAlpha(0.5f),
                                                }
                                                .ObserveProperties(this, [nameof(CurrentTimer)], me =>
                                                {
                                                    if (CurrentTimer != null)
                                                    {
                                                        //find index
                                                        var available = -1;
                                                        var index = -1;
                                                        foreach (var timer in Timers)
                                                        {
                                                            available++;
                                                            if (timer.Id == CurrentTimer.Id)
                                                            {
                                                                index = available;
                                                                break;
                                                            }
                                                        }

                                                        if (index > 0)
                                                        {
                                                            me.Opacity = 1;
                                                            me.InputTransparent = false;
                                                        }
                                                        else
                                                        {
                                                            me.Opacity = 0.5;
                                                            me.InputTransparent = true;
                                                        }
                                                    }
                                                })
                                                .OnTapped(me => { SelectPreviousTimer(); }),


                                            //PLAY | PAUSE
                                            new FontIconLabelDrawn()
                                                {
                                                    Text = FaPro.CirclePlay,
                                                    FontSize = 44,
                                                    TextColor = TextColor.WithAlpha(0.5f),
                                                }
                                                .ObserveProperties(this, [nameof(IsRunning), nameof(IsPaused)], me =>
                                                {
                                                    if (IsRunning && !IsPaused)
                                                    {
                                                        me.Text = FaPro.CirclePause;
                                                    }
                                                    else
                                                    {
                                                        me.Text = FaPro.CirclePlay;
                                                    }
                                                })
                                                .OnTapped(me =>
                                                {
                                                    if (IsRunning)
                                                    {
                                                        PauseTimer();
                                                    }
                                                    else
                                                    {
                                                        StartTimer();
                                                    }
                                                }),

                                            //STOP
                                            new FontIconLabelDrawn()
                                                {
                                                    Text = FaPro.CircleStop,
                                                    FontSize = 44,
                                                    TextColor = TextColor.WithAlpha(0.5f),
                                                }
                                                .ObserveProperties(this, [nameof(IsRunning), nameof(IsPaused), nameof(CurrentTimer)], me =>
                                                {
                                                    if (IsRunning || IsPaused)
                                                    {
                                                        me.Opacity = 1;
                                                        me.InputTransparent = false;
                                                    }
                                                    else
                                                    {
                                                        //find index
                                                        var available = -1;
                                                        var index = -1;
                                                        foreach (var timer in Timers)
                                                        {
                                                            available++;
                                                            if (timer.Id == CurrentTimer.Id)
                                                            {
                                                                index = available;
                                                                break;
                                                            }
                                                        }

                                                        if (index != 0)
                                                        {
                                                            me.Opacity = 1;
                                                            me.InputTransparent = false;
                                                        }
                                                        else
                                                        {
                                                            me.Opacity = 0.5;
                                                            me.InputTransparent = true;
                                                        }

                                                    }
                                                })
                                                .OnTapped(me =>
                                                {
                                                    if (IsRunning || IsPaused)
                                                    {
                                                        StopTimer(true);
                                                    }
                                                    else
                                                    {
                                                        //total reset go to first timer
                                                        SelectFirstTimer();
                                                    }
                                                }),

                                            //NEXT
                                            new FontIconLabelDrawn()
                                                {
                                                    Text = FaPro.CircleRight,
                                                    FontSize = 44,
                                                    TextColor = TextColor.WithAlpha(0.5f),
                                                }
                                                .ObserveProperties(this, [nameof(CurrentTimer)], me =>
                                                {
                                                    if (CurrentTimer != null)
                                                    {
                                                        //find index
                                                        var available = -1;
                                                        var index = -1;
                                                        foreach (var timer in Timers)
                                                        {
                                                            available++;
                                                            if (timer.Id == CurrentTimer.Id)
                                                            {
                                                                index = available;
                                                                break;
                                                            }
                                                        }

                                                        if (index < Timers.Count - 1)
                                                        {
                                                            me.Opacity = 1;
                                                            me.InputTransparent = false;
                                                        }
                                                        else
                                                        {
                                                            me.Opacity = 0.5;
                                                            me.InputTransparent = true;
                                                        }
                                                    }
                                                })
                                                .OnTapped(me => { SelectNextTimer(); }),
                                        }
                                    },
                                    new SkiaLayer()
                                    {
                                        VerticalOptions = LayoutOptions.Fill,
                                        Children =
                                        {
                                            new SkiaStack()
                                            {
                                                VerticalOptions = LayoutOptions.End,
                                                //ItemsSource = Timers,
                                                ItemTemplate = new DataTemplate(() =>
                                                {
                                                    SkiaLabel cellTitle;
                                                    SkiaLabel cellTime;
                                                    SkiaProgress cellProgress;
                                                    var cell = new SkiaGrid()
                                                        {
                                                            ColumnSpacing = 8,
                                                            Children =
                                                            {
                                                                new SkiaRichLabel()
                                                                    {
                                                                        TextColor = TextColor,
                                                                        FontFamily = AppFonts.Title,
                                                                        HorizontalOptions = LayoutOptions.End
                                                                    }
                                                                    .Assign(out cellTitle).WithColumn(0),
                                                                new SkiaRichLabel()
                                                                    {
                                                                        TextColor = TextColor,
                                                                        FontFamily = AppFonts.Title
                                                                    }
                                                                    .Assign(out cellTime).WithColumn(1),
                                                                new SkiaProgress()
                                                                    {
                                                                        VerticalOptions = LayoutOptions.Center,
                                                                        ProgressColor = TextColor,
                                                                        TrackColor = TextColor.WithAlpha(0.5f)
                                                                    }
                                                                    .Assign(out cellProgress).WithColumn(2)
                                                            }
                                                        }.WithColumnDefinitions("*,Auto,80")
                                                        .ObserveProperties(this, [nameof(CurrentTimer)], me =>
                                                        {
                                                            if (CurrentTimer != null &&
                                                                me.BindingContext is RunningTimer ctx)
                                                            {
                                                                cellTitle.FontAttributes = CurrentTimer.Id == ctx.Id
                                                                    ? FontAttributes.Bold
                                                                    : FontAttributes.None;
                                                            }
                                                            else
                                                            {
                                                                cellTitle.FontAttributes = FontAttributes.None;
                                                            }
                                                        })
                                                        .ObserveBindingContext<SkiaLayout,
                                                            RunningTimer>((me, ctx, prop) =>
                                                        {
                                                            if (prop.IsEither(nameof(BindingContext)))
                                                            {
                                                                //initial cell setup
                                                                cellProgress.Value = ctx.Progress;
                                                                cellTime.Text = $"{ctx.Time:mm\\:ss}";
                                                                cellTitle.Text = ctx.Timer.Title;

                                                                if (CurrentTimer != null)
                                                                {
                                                                    cellTitle.FontAttributes = CurrentTimer.Id == ctx.Id
                                                                        ? FontAttributes.Bold
                                                                        : FontAttributes.None;
                                                                }
                                                                else
                                                                {
                                                                    cellTitle.FontAttributes = FontAttributes.None;
                                                                }
                                                            }
                                                            else
                                                                //some specific props changing
                                                            if (prop == nameof(RunningTimer.Progress))
                                                            {
                                                                cellProgress.Value = ctx.Progress;
                                                            }
                                                            else if (prop == nameof(RunningTimer.Time))
                                                            {
                                                                cellTime.Text = $"{ctx.Time:mm\\:ss}";
                                                            }
                                                        });
                                                    return cell;
                                                })
                                            }.ObserveProperty(this, nameof(Timers), me =>
                                            {
                                                if (me.ItemsSource == Timers)
                                                {
                                                    //need refresh if ref is same but collection changed
                                                    me.ApplyItemsSource();
                                                }
                                                else
                                                {
                                                    //will rebuild for new
                                                    me.ItemsSource = Timers;
                                                }
                                            })
                                        }
                                    },

                                    //BUTTONS SETTINGS ETC
                                    new SkiaGrid()
                                    {
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Margin = 24,
                                        ColumnSpacing = 16,
                                        Children =
                                        {

                                            // EXIT
                                            new SkiaShape()
                                            {
                                                StrokeColor = TextColor,
                                                StrokeWidth = StrokeWidth,
                                                VerticalOptions = LayoutOptions.Start,
                                                UseCache = SkiaCacheType.Image,
                                                CornerRadius = 12,
                                                HeightRequest = 38,
                                                WidthRequest = 150,
                                                BackgroundColor = Colors.Transparent,
                                                Children =
                                                {
                                                    new SkiaRichLabel()
                                                    {
                                                        HorizontalOptions = LayoutOptions.Center,
                                                        VerticalOptions = LayoutOptions.Center,
                                                        MaxLines = 1,
                                                        FontSize = 14,
                                                        TextColor = TextColor,
                                                        Text = ResStrings.Exit,
                                                        FontFamily = AppFonts.Title
                                                    }
                                                }
                                            }.OnTapped(me =>
                                            {
                                                ExitScreen();

                                            }).WithColumn(0),

                                            //SETTINGS
                                            new SkiaShape()
                                            {
                                                HorizontalOptions = LayoutOptions.End,
                                                StrokeColor = TextColor,
                                                StrokeWidth = StrokeWidth,
                                                VerticalOptions = LayoutOptions.Start,
                                                UseCache = SkiaCacheType.Image,
                                                CornerRadius = 12,
                                                HeightRequest = 38,
                                                WidthRequest = 150,
                                                BackgroundColor = Colors.Transparent,
                                                Children =
                                                {
                                                    new SkiaRichLabel(ResStrings.Settings)
                                                        {
                                                            HorizontalOptions = LayoutOptions.Center,
                                                            VerticalOptions = LayoutOptions.Center,
                                                            MaxLines = 1,
                                                            FontSize = 14,
                                                            TextColor = TextColor,
                                                            FontFamily = AppFonts.Title
                                                        }
                                                        .ObserveProperties(this, [nameof(IsRunning), nameof(IsPaused)],
                                                            me =>
                                                            {
                                                                if (IsPaused)
                                                                {
                                                                    me.Text = ResStrings.Resume;
                                                                }
                                                                else if (IsRunning)
                                                                {
                                                                    me.Text = ResStrings.Pause;
                                                                }
                                                                else
                                                                {
                                                                    me.Text = ResStrings.Settings;
                                                                }
                                                            })
                                                }
                                            }.OnTapped((me) =>
                                            {
                                                if (IsPaused)
                                                {
                                                    StartTimer();
                                                }
                                                else if (IsRunning)
                                                {
                                                    PauseTimer();
                                                }
                                                else
                                                {
                                                    OpenSettings();
                                                }
                                            }).WithColumn(1),

                                        }
                                    }.WithColumnDefinitions("50*,50*")

                                }
                            }.Observe(this, (me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext), nameof(VisibleScreen)))
                                {
                                    me.IsVisible = VisibleScreen == 0;
                                }
                            }),

                            //SETTINGS SCREEN
                            new SkiaLayer()
                            {
                                Tag = "Settings-1",
                                VerticalOptions = LayoutOptions.Fill,
                                Type = LayoutType.Column,
                                Spacing = 24,
                                Padding = new(16, 24, 16, 24),
                                HorizontalOptions = LayoutOptions.Fill,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaRichLabel(ResStrings.Settings)
                                    {
                                        UseCache = SkiaCacheType.Operations,
                                        HorizontalOptions = LayoutOptions.Center,
                                        FontSize = 24,
                                        MaxLines = 1,
                                        TextColor = TextColor,
                                        FontFamily = AppFonts.Title
                                    },

                                    new SkiaRichLabel(ResStrings.Skin)
                                    {
                                        UseCache = SkiaCacheType.Operations,
                                        HorizontalOptions = LayoutOptions.Center,
                                        FontSize = 18,
                                        MaxLines = 1,
                                        TextColor = TextColor,
                                        FontFamily = AppFonts.Title
                                    },

                                    //SKIN radio
                                    new SkiaWrap()
                                    {
                                        UseCache = SkiaCacheType.Image,
                                        Spacing = 16,
                                        HorizontalOptions = LayoutOptions.Center,
                                        Children =
                                        {
                                            new DrawnRadioButton(TextColor)
                                            {
                                                Text = ResStrings.SkinRed,
                                                VerticalOptions = LayoutOptions.Center
                                            },
                                            new DrawnRadioButton(TextColor)
                                            {
                                                Text = ResStrings.White,
                                                VerticalOptions = LayoutOptions.Center
                                            },
                                        }
                                    }.Assign(out RadiosSkin),

                                    //STEPS
                                    new SkiaLabel(ResStrings.Steps)
                                    {
                                        UseCache = SkiaCacheType.Operations,
                                        Margin = new(0, 16, 0, 0),
                                        HorizontalOptions = LayoutOptions.Center,
                                        FontSize = 18,
                                        MaxLines = 1,
                                        TextColor = TextColor,
                                        FontFamily = AppFonts.Title
                                    },

                                    new SkiaShape()
                                    {
                                        UseCache = SkiaCacheType.Image,
                                        HorizontalOptions = LayoutOptions.Center,
                                        StrokeColor = TextColor,
                                        StrokeWidth = StrokeWidth,
                                        VerticalOptions = LayoutOptions.Start,
                                        CornerRadius = 12,
                                        HeightRequest = 38,
                                        WidthRequest = 150,
                                        BackgroundColor = Colors.Transparent,
                                        Children =
                                        {
                                            new SkiaRichLabel(ResStrings.BtnCreate)
                                            {
                                                HorizontalOptions = LayoutOptions.Center,
                                                VerticalOptions = LayoutOptions.Center,
                                                MaxLines = 1,
                                                FontSize = 14,
                                                TextColor = TextColor,
                                                FontFamily = AppFonts.Title
                                            }
                                        }
                                    }.OnTapped((me) =>
                                    {
                                        Debug.WriteLine("ADD");
                                        EditItem(null);
                                    }),

                                    //EDITABLE LIST
                                    new SkiaScroll()
                                    {
                                        VerticalOptions = LayoutOptions.Fill,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        //Bounces = false,
                                        Content = new SkiaStack()
                                        {
                                            UseCache = SkiaCacheType.Image,
                                            //ItemsSource = EditingTimers,
                                            ItemTemplate = new DataTemplate(() =>
                                            {
                                                SkiaLabel cellTitle;
                                                SkiaLabel cellTime;

                                                var cell = new SkiaGrid()
                                                    {
                                                        UseCache = SkiaCacheType.Image,
                                                        HeightRequest = 38,
                                                        ColumnSpacing = 8,
                                                        Children =
                                                        {
                                                            //time
                                                            new SkiaRichLabel()
                                                                {
                                                                    TextColor = TextColor,
                                                                    FontFamily = AppFonts.Title,
                                                                    VerticalOptions = LayoutOptions.Center
                                                                }
                                                                .Assign(out cellTime).WithColumn(0),

                                                            // title
                                                            new SkiaRichLabel()
                                                                {
                                                                    TextColor = TextColor,
                                                                    FontFamily = AppFonts.Title,
                                                                    MaxLines = 1,
                                                                    VerticalOptions = LayoutOptions.Fill,
                                                                    HorizontalOptions = LayoutOptions.Fill,
                                                                    VerticalTextAlignment = TextAlignment.Center
                                                                }
                                                                .OnTapped(me =>
                                                                {
                                                                    EditItem(me.BindingContext as TimerStep);
                                                                })
                                                                //.OnLongPressing(me => EditItem(me.BindingContext as TimerStep))
                                                                .Assign(out cellTitle).WithColumn(1),


                                                            // DELETE
                                                            new FontIconLabelDrawn()
                                                                {
                                                                    Text = FaPro.TrashCan,
                                                                    FontSize = 14,
                                                                    TextColor = TextColor.WithAlpha(0.85f),
                                                                    HorizontalOptions = LayoutOptions.Center,
                                                                    VerticalOptions = LayoutOptions.Center,
                                                                    Margin = new Thickness(0, 0, 12, 0)
                                                                }
                                                                .OnTapped(me =>
                                                                    DeleteItem(me.BindingContext as TimerStep))
                                                                .WithColumn(2),
                                                        }
                                                    }.WithColumnDefinitions("Auto,*,32")
                                                    .ObserveSelf((me, prop) =>
                                                    {
                                                        if (me.BindingContext is TimerStep ctx)
                                                        {
                                                            if (prop.IsEither(nameof(BindingContext)))
                                                            {
                                                                cellTime.Text = $"{ctx.Time:mm\\:ss}";
                                                                cellTitle.Text = ctx.Title;
                                                            }
                                                        }
                                                    });

                                                return cell;
                                            })
                                        }.ObserveProperty(this, nameof(EditingTimers), me =>
                                        {
                                            if (EditingTimers == me.ItemsSource)
                                            {
                                                me.ApplyItemsSource(); //rebuild
                                            }
                                            else
                                            {
                                                me.ItemsSource = EditingTimers;
                                            }
                                        }),
                                    },

                                    //BUTTONS SAVE ETC
                                    new SkiaGrid()
                                    {
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Margin = new(24, 0, 24, 16),
                                        ColumnSpacing = 16,
                                        Children =
                                        {
                                            new SkiaShape()
                                            {
                                                HorizontalOptions = LayoutOptions.End,
                                                StrokeColor = TextColor,
                                                StrokeWidth = StrokeWidth,
                                                VerticalOptions = LayoutOptions.Start,
                                                UseCache = SkiaCacheType.Image,
                                                CornerRadius = 12,
                                                HeightRequest = 38,
                                                WidthRequest = 150,
                                                BackgroundColor = Colors.Transparent,
                                                Children =
                                                {
                                                    new SkiaRichLabel(ResStrings.BtnClose)
                                                    {
                                                        HorizontalOptions = LayoutOptions.Center,
                                                        VerticalOptions = LayoutOptions.Center,
                                                        MaxLines = 1,
                                                        FontSize = 14,
                                                        TextColor = TextColor,
                                                        FontFamily = AppFonts.Title
                                                    }
                                                }
                                            }.OnTapped((me) =>
                                            {
                                                Debug.WriteLine("CANCEL");
                                                VisibleScreen = 0;
                                            }),
                                            new SkiaShape()
                                            {
                                                StrokeColor = TextColor,
                                                StrokeWidth = StrokeWidth,
                                                VerticalOptions = LayoutOptions.Start,
                                                UseCache = SkiaCacheType.Image,
                                                CornerRadius = 12,
                                                HeightRequest = 38,
                                                WidthRequest = 150,
                                                BackgroundColor = Colors.Transparent,
                                                Children =
                                                {
                                                    new SkiaRichLabel(ResStrings.BtnSave)
                                                    {
                                                        HorizontalOptions = LayoutOptions.Center,
                                                        VerticalOptions = LayoutOptions.Center,
                                                        MaxLines = 1,
                                                        FontSize = 14,
                                                        TextColor = TextColor,
                                                        FontFamily = AppFonts.Title
                                                    }
                                                }
                                            }.OnTapped((me) =>
                                            {
                                                Debug.WriteLine("SAVE");
                                                SaveSettings();
                                            }).WithColumn(1),
                                        }
                                    }.WithColumnDefinitions("50*,50*")
                                }
                            }.Observe(this, (me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext), nameof(VisibleScreen)))
                                {
                                    me.IsVisible = VisibleScreen == 1;
                                }
                            }),

                            //EDIT SCREEN
                            new SkiaLayer()
                            {
                                Tag = "EDITOR-2",
                                VerticalOptions = LayoutOptions.Fill,
                                Type = LayoutType.Column,
                                Spacing = 24,
                                Padding = new(16, 24, 16, 24),
                                HorizontalOptions = LayoutOptions.Fill,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaRichLabel(ResStrings.BtnEdit)
                                    {
                                        HorizontalOptions = LayoutOptions.Center,
                                        FontSize = 24,
                                        MaxLines = 1,
                                        TextColor = TextColor,
                                        FontFamily = AppFonts.Title
                                    }.Assign(out LabelEditorTitle),
                                    new SkiaRichLabel(ResStrings.Title)
                                    {
                                        HorizontalOptions = LayoutOptions.Center,
                                        FontSize = 18,
                                        MaxLines = 1,
                                        TextColor = TextColor,
                                        FontFamily = AppFonts.Title
                                    },

                                    // ENTRY for TITLE
                                    new SkiaShape()
                                    {
                                        StrokeWidth = StrokeWidth,
                                        StrokeColor = TextColor,
                                        CornerRadius = 8,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        HeightRequest = 44,
                                        Padding = new(6, 0),
                                        Children =
                                        {
                                            new SkiaMauiEntry()
                                            {
                                                VerticalOptions = LayoutOptions.Fill,
                                                TextColor = TextColor,
                                                FontSize = 15,
                                                FontFamily = AppFonts.Default,
                                                Placeholder = "...",
                                                PlaceholderColor = TextColor.WithAlpha(0.75f),
                                                HorizontalOptions = LayoutOptions.Fill
                                            }.Assign(out EntryEditor)
                                        }
                                    },

                                    //TIME WHEELS
                                    new SkiaLabel(ResStrings.Time)
                                    {
                                        Margin = new(0, 4, 0, 0),
                                        HorizontalOptions = LayoutOptions.Center,
                                        FontSize = 18,
                                        MaxLines = 1,
                                        TextColor = TextColor,
                                        FontFamily = AppFonts.Title
                                    },
                                    new SkiaRow()
                                    {
                                        HeightRequest = 200,
                                        HorizontalOptions = LayoutOptions.Center,
                                        Children =
                                        {
                                            // MINS
                                            new SkiaWheelPicker()
                                                {
                                                    BackgroundView =
                                                        new SkiaLayer()
                                                        {
                                                            VerticalOptions = LayoutOptions.Fill,
                                                            FillGradient = pickerGradient,
                                                        },
                                                    WidthRequest = 75,
                                                    VisibleItems = 7,
                                                    UseCache = SkiaCacheType.Operations,
                                                    HeightRequest = -1,
                                                    HorizontalOptions = LayoutOptions.Start,
                                                    VerticalOptions = LayoutOptions.Fill,
                                                    LinesColor = TextColor.MultiplyAlpha(0.33f),
                                                    TextColor = TextColor,
                                                    ItemsSource = MinutesSource,
                                                }.Assign(out PickerMinutes)
                                                //two-way bindings
                                                //way A
                                                .ObserveSelf((me, prop) =>
                                                {
                                                    if (prop.IsEither(nameof(BindingContext),
                                                            nameof(WheelPicker.SelectedIndex)))
                                                    {
                                                        SelectedMinutesIndex = me.SelectedIndex;
                                                    }
                                                })
                                                //way B
                                                .Observe(this, (me, prop) =>
                                                {
                                                    if (prop.IsEither(nameof(BindingContext),
                                                            nameof(SelectedMinutesIndex)))
                                                    {
                                                        me.SelectedIndex = SelectedMinutesIndex;
                                                    }
                                                }),
                                            new SkiaRichLabel(ResStrings.X_Mins)
                                            {
                                                VerticalOptions = LayoutOptions.Center,
                                                FontSize = 14,
                                                MaxLines = 1,
                                                TextColor = TextColor,
                                                FontFamily = AppFonts.Title
                                            },

                                            //SECS
                                            new SkiaWheelPicker()
                                                {
                                                    Margin = new(8, 0, 0, 0),
                                                    BackgroundView =
                                                        new SkiaLayer()
                                                        {
                                                            VerticalOptions = LayoutOptions.Fill,
                                                            FillGradient = pickerGradient,
                                                        },
                                                    WidthRequest = 75,
                                                    VisibleItems = 7,
                                                    UseCache = SkiaCacheType.Operations,
                                                    HeightRequest = -1,
                                                    HorizontalOptions = LayoutOptions.Start,
                                                    VerticalOptions = LayoutOptions.Fill,
                                                    LinesColor = TextColor.MultiplyAlpha(0.33f),
                                                    TextColor = TextColor,
                                                    ItemsSource = SecondsSource,
                                                }.Assign(out PickerSeconds)
                                                //two-way bindings
                                                //way A
                                                .ObserveSelf((me, prop) =>
                                                {
                                                    if (prop.IsEither(nameof(BindingContext),
                                                            nameof(SkiaWheelPicker.SelectedIndex)))
                                                    {
                                                        SelectedSecondsIndex = me.SelectedIndex;
                                                    }
                                                })
                                                //way B
                                                .Observe(this, (me, prop) =>
                                                {
                                                    if (prop.IsEither(nameof(BindingContext),
                                                            nameof(SelectedSecondsIndex)))
                                                    {
                                                        me.SelectedIndex = SelectedSecondsIndex;
                                                    }
                                                }),
                                            new SkiaRichLabel(ResStrings.X_Secs)
                                            {
                                                VerticalOptions = LayoutOptions.Center,
                                                FontSize = 14,
                                                MaxLines = 1,
                                                TextColor = TextColor,
                                                FontFamily = AppFonts.Title
                                            },
                                        }
                                    },

                                    //BUTTONS SAVE ETC
                                    new SkiaGrid()
                                    {
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Margin = new(24, 14, 24, 16),
                                        ColumnSpacing = 16,
                                        Children =
                                        {
                                            // CANCEL
                                            new SkiaShape()
                                            {
                                                HorizontalOptions = LayoutOptions.End,
                                                StrokeColor = TextColor,
                                                StrokeWidth = StrokeWidth,
                                                VerticalOptions = LayoutOptions.Start,
                                                UseCache = SkiaCacheType.Image,
                                                CornerRadius = 12,
                                                HeightRequest = 38,
                                                WidthRequest = 150,
                                                BackgroundColor = Colors.Transparent,
                                                Children =
                                                {
                                                    new SkiaRichLabel(ResStrings.BtnCancel)
                                                    {
                                                        HorizontalOptions = LayoutOptions.Center,
                                                        VerticalOptions = LayoutOptions.Center,
                                                        MaxLines = 1,
                                                        FontSize = 14,
                                                        TextColor = TextColor,
                                                        FontFamily = AppFonts.Title
                                                    }
                                                }
                                            }.OnTapped((me) =>
                                            {
                                                Debug.WriteLine("CANCEL");
                                                GoBackFromEditor();
                                            }),

                                            //SAVE
                                            new SkiaShape()
                                            {
                                                StrokeColor = TextColor,
                                                StrokeWidth = StrokeWidth,
                                                VerticalOptions = LayoutOptions.Start,
                                                UseCache = SkiaCacheType.Image,
                                                CornerRadius = 12,
                                                HeightRequest = 38,
                                                WidthRequest = 150,
                                                BackgroundColor = Colors.Transparent,
                                                Children =
                                                {
                                                    new SkiaRichLabel(ResStrings.BtnSave)
                                                    {
                                                        HorizontalOptions = LayoutOptions.Center,
                                                        VerticalOptions = LayoutOptions.Center,
                                                        MaxLines = 1,
                                                        FontSize = 14,
                                                        TextColor = TextColor,
                                                        FontFamily = AppFonts.Title
                                                    }
                                                }
                                            }.OnTapped((me) =>
                                            {
                                                Debug.WriteLine("SAVE");
                                                SaveEditor();
                                            }).WithColumn(1),
                                        }
                                    }.WithColumnDefinitions("50*,50*")
                                }
                            }.Observe(this, (me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext), nameof(VisibleScreen)))
                                {
                                    me.IsVisible = VisibleScreen == 2;
                                }
                            })
                        },
                        new SkiaLayer() //bottom navbar
                        {
                            HeightRequest = Super.Screen.BottomInset
                        },
                    }
                }
            };

            RadioButtons.All.Select(RadiosSkin, Preferences.Get("TimerSkin", 0));
        }

        private SkiaLayout RadiosSkin;

        private void OnRadioChanged(object? sender, EventArgs e)
        {
            if (RadiosSkin != null)
            {
                var selected = RadioButtons.All.GetSelectedIndex(RadiosSkin);
                if (selected >= 0)
                {
                    SelectSkin(selected);
                }
            }
        }

        #endregion

        #region EDITOR

        private SkiaMauiEntry? EntryEditor;
        private SkiaLabel LabelEditorTitle;

        /// <summary>
        /// Add or Edit item
        /// </summary>
        /// <param name="item"></param>
        private void EditItem(TimerStep? item)
        {
            EditingItem = item;

            if (item == null)
            {
                //create
                Item = new() { Title = "", Time = new TimeSpan(0, 1, 0) };
                LabelEditorTitle.Text = ResStrings.NewStep;
            }
            else
            {
                //edit
                Item = Reflection.Clone(item);
                LabelEditorTitle.Text = ResStrings.EditStep;
            }

            SetupEditor();

            VisibleScreen = 2;
        }

        void OpenSettings()
        {
            EditingTimers = Reflection.Clone(AvailableTimers);
            VisibleScreen = 1;
        }


        /// <summary>
        /// Save settings - update AvailableTimers from EditingTimers and rebuild Timers collection
        /// </summary>
        void SaveSettings()
        {
            // Update AvailableTimers from EditingTimers
            AvailableTimers = Reflection.Clone(EditingTimers);

            // Rebuild Timers collection to match AvailableTimers
            RebuildTimersFromAvailable();

            // Save to preferences
            TimerStep.Save(AvailableTimers);

            VisibleScreen = 0;
        }

        void PopulateTimers()
        {
            foreach (TimerStep availableTimer in AvailableTimers)
            {
                Timers.Add(new RunningTimer()
                {
                    Timer = availableTimer, Id = availableTimer.Id, Time = availableTimer.Time
                });
            }
        }

        /// <summary>
        /// Rebuild Timers collection from AvailableTimers, preserving current timer selection if possible
        /// </summary>
        void RebuildTimersFromAvailable()
        {
            var currentTimerId = CurrentTimer?.Id;

            Timers.Clear();
            PopulateTimers();

            // Try to restore current timer selection
            if (currentTimerId.HasValue)
            {
                var restoredTimer = Timers.FirstOrDefault(t => t.Id == currentTimerId.Value);
                if (restoredTimer != null)
                {
                    CurrentTimer = restoredTimer;
                    CurrentTimer.Time = CurrentTimer.Timer.Time;
                    CurrentTimer.Progress = 0;
                }
                else if (Timers.Count > 0)
                {
                    CurrentTimer = Timers[0];
                    CurrentTimer.Time = CurrentTimer.Timer.Time;
                    CurrentTimer.Progress = 0;
                }
            }
            else if (Timers.Count > 0)
            {
                CurrentTimer = Timers[0];
                CurrentTimer.Time = CurrentTimer.Timer.Time;
                CurrentTimer.Progress = 0;
            }

            OnPropertyChanged(nameof(Timers));

            if (Timers.Count > 0)
            {
                SetTimerAsCurrent(Timers[0]);
            }
        }

        private TimerStep? EditingItem;

        void SetupEditor()
        {
            EntryEditor.Text = Item.Title;

            var mins = Item.Time.Minutes.ToString();
            var secs = Item.Time.Seconds.ToString();
            var itemMins = Minutes.FirstOrDefault(x => x.Title == mins);
            var itemSecs = Minutes.FirstOrDefault(x => x.Title == secs);

            PickerMinutes.SelectedIndex = itemMins != null ? Minutes.IndexOf(itemMins) : 0;
            PickerSeconds.SelectedIndex = itemSecs != null ? Minutes.IndexOf(itemSecs) : 0;
        }

        void SaveEditor()
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(EntryEditor?.Text))
            {
                // Could show an error message here
                return;
            }

            // Get values from UI
            var title = EntryEditor.Text.Trim();
            var minutes = PickerMinutes.SelectedIndex >= 0 && PickerMinutes.SelectedIndex < MinutesSource.Count
                ? MinutesSource[PickerMinutes.SelectedIndex].Value
                : 0;
            var seconds = PickerSeconds.SelectedIndex >= 0 && PickerSeconds.SelectedIndex < SecondsSource.Count
                ? SecondsSource[PickerSeconds.SelectedIndex].Value
                : 0;

            var timeSpan = new TimeSpan(0, (int)minutes, (int)seconds);

            if (timeSpan.TotalSeconds <= 0)
            {
                // Timer must be at least 1 second
                return;
            }

            if (EditingItem == null) //new
            {
                // Create new timer step and add to EditingTimers
                var newStep = new TimerStep { Title = title, Time = timeSpan };
                EditingTimers.Add(newStep);
            }
            else
            {
                // Update existing timer step in EditingTimers
                var editingStep = EditingTimers.FirstOrDefault(t => t.Id == EditingItem.Id);
                if (editingStep != null)
                {
                    editingStep.Title = title;
                    editingStep.Time = timeSpan;
                }
            }

            OnPropertyChanged(nameof(EditingTimers));

            GoBackFromEditor();
        }

        void GoBackFromEditor()
        {
            VisibleScreen = 1;
        }

        /// <summary>
        /// MODEL FOR EDITOR
        /// </summary>
        protected TimerStep Item { get; set; }

        private void DeleteItem(TimerStep item)
        {
            if (item == null) return;

            // Remove from EditingTimers collection
            EditingTimers.Remove(item);
            OnPropertyChanged(nameof(EditingTimers));
        }

        #endregion

        #region PICKER TIME

        void InitPickers()
        {
            Minutes = new List<ValueItem>();
            Minutes.AddRange(Enumerable.Range(0, 60).Select(x => new ValueItem { Title = x.ToString(), Value = x })
                .ToList());

            SecondsSource = Minutes.ToList();
            MinutesSource = Minutes.ToList();
        }

        /// <summary>
        /// Data source for wheel picker
        /// </summary>
        public List<ValueItem> MinutesSource { get; set; }

        /// <summary>
        /// Data source for wheel picker
        /// </summary>
        public List<ValueItem> SecondsSource { get; set; }

        private SkiaWheelPicker PickerMinutes;
        private SkiaWheelPicker PickerSeconds;

        private ValueItem _selectedSeconds;

        public ValueItem SelectedSeconds
        {
            get { return _selectedSeconds; }
            set
            {
                if (_selectedSeconds != value)
                {
                    _selectedSeconds = value;
                    OnPropertyChanged();
                }
            }
        }

        public List<ValueItem> Minutes { get; set; }


        private int _SelectedMinutesIndex = -1;

        public int SelectedMinutesIndex
        {
            get { return _SelectedMinutesIndex; }
            set
            {
                if (_SelectedMinutesIndex != value)
                {
                    _SelectedMinutesIndex = value;
                    OnPropertyChanged();
                }
            }
        }

        private int _SelectedSecondsIndex = -1;
        private List<TimerStep> editingTimers;
        private List<TimerStep> availableTimers = new();

        public int SelectedSecondsIndex
        {
            get { return _SelectedSecondsIndex; }
            set
            {
                if (_SelectedSecondsIndex != value)
                {
                    _SelectedSecondsIndex = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion
    }
}
